/**
 * 世界树VCP插件
 * 独立的世界树系统，支持时间架构、角色日程表和心理活动生成
 * 集成现有的AdvancedMemorySystem数据库，使用本地算法生成心理状态
 */

const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();
const { promisify } = require('util');
const moment = require('moment');
const natural = require('natural');
const { Matrix } = require('ml-matrix');
const similarity = require('similarity');
const axios = require('axios');
const dotenv = require('dotenv');

// 导入科学算法模块
const ScientificPsychologyAlgorithms = require('./ScientificPsychologyAlgorithms');
const ChronobiologyModule = require('./ChronobiologyModule');
const EmotionalComputingModule = require('./EmotionalComputingModule');
const PhysicalStateAnalyzer = require('./PhysicalStateAnalyzer');
const IntegratedPsychologyAnalyzer = require('./IntegratedPsychologyAnalyzer');

class WorldTreeVCP {
    constructor() {
        this.pluginName = 'WorldTreeVCP';
        this.version = '1.0.0';
        this.description = '世界树VCP插件 - 时间架构与角色心理活动系统';
        this.isInitialized = false;
        this.logger = null;

        // 数据库连接 - 使用现有的emotion_memory.db
        this.db = null;
        this.dbPath = path.join(__dirname, '../AdvancedMemorySystem/data/emotion_memory.db');

        // 加载配置（优先使用插件专用配置）
        this.config = this.loadPluginConfig();

        // 初始化科学算法模块
        this.scientificAlgorithms = new ScientificPsychologyAlgorithms(this.config);
        this.chronobiology = new ChronobiologyModule(this.config);
        this.emotionalComputing = new EmotionalComputingModule(this.config);
        this.physicalAnalyzer = new PhysicalStateAnalyzer(this.config);
        this.integratedAnalyzer = new IntegratedPsychologyAnalyzer(this.config);

        // 调用机制控制
        this.lastRequestTime = 0;
        this.lastAutoUpdateTime = Date.now(); // 初始化为当前时间，避免第一次计算异常
        
        // 世界树配置存储
        this.worldTreeConfigs = new Map();
        this.configCacheTimestamps = new Map(); // 缓存时间戳
        
        // 心理状态算法参数
        this.psychologyAlgorithm = {
            // 基础心理状态权重
            baseWeights: {
                stress: 0.3,
                emotion: 0.25,
                energy: 0.2,
                mood: 0.15,
                focus: 0.1
            },
            
            // 时间因素影响
            timeFactors: {
                morning: { energy: 1.2, focus: 1.1, mood: 1.0 },
                afternoon: { energy: 0.9, focus: 1.0, mood: 1.1 },
                evening: { energy: 0.7, focus: 0.8, mood: 1.2 },
                night: { energy: 0.5, focus: 0.6, mood: 0.9 }
            },
            
            // 任务类型影响
            taskFactors: {
                creative: { energy: 0.8, focus: 1.2, mood: 1.1 },
                analytical: { energy: 1.0, focus: 1.3, mood: 0.9 },
                social: { energy: 1.1, focus: 0.9, mood: 1.3 },
                routine: { energy: 0.9, focus: 1.0, mood: 1.0 }
            }
        };
        
        // 初始化标志
        this.isInitialized = false;
        
        // 日志记录器
        this.logger = null;
    }

    /**
     * 插件初始化
     */
    async initialize(logger) {
        try {
            this.logger = logger || console;

            // 重新加载配置（现在有logger了，可以记录详细信息）
            this.config = this.loadPluginConfig();

            // 记录配置信息
            this.logger.info('世界树VCP', '配置加载完成:', {
                apiUrl: this.config.apiUrl,
                model: this.config.model,
                useLocalAlgorithm: this.config.useLocalAlgorithm,
                hasApiKey: !!this.config.apiKey,
                timeout: this.config.timeout
            });

            // 初始化数据库连接
            await this.initializeDatabase();

            // 创建必要的表结构
            await this.createTables();

            // 启动心理状态更新定时器
            this.startPsychologyUpdateTimer();

            this.isInitialized = true;
            this.logger.info('世界树VCP', '插件初始化成功');

            return true;
        } catch (error) {
            this.logger.error('世界树VCP', '插件初始化失败:', error.message);
            return false;
        }
    }

    /**
     * 初始化数据库连接
     */
    async initializeDatabase() {
        return new Promise((resolve, reject) => {
            // 确保数据库目录存在
            const dbDir = path.dirname(this.dbPath);
            fs.mkdir(dbDir, { recursive: true }).then(() => {
                this.db = new sqlite3.Database(this.dbPath, (err) => {
                    if (err) {
                        reject(new Error(`数据库连接失败: ${err.message}`));
                    } else {
                        // 启用外键约束
                        this.db.run('PRAGMA foreign_keys = ON');
                        resolve();
                    }
                });
            }).catch(reject);
        });
    }

    /**
     * 数据库操作封装
     */
    dbRun(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) reject(err);
                else resolve({ lastID: this.lastID, changes: this.changes });
            });
        });
    }

    dbGet(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });
    }

    dbAll(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
    }

    /**
     * 创建世界树相关表结构
     */
    async createTables() {
        // 创建世界树相关表结构
        const tables = [
            // 世界树配置表（如果不存在则创建）
            `CREATE TABLE IF NOT EXISTS world_tree_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                agent_name TEXT NOT NULL,
                config_data TEXT NOT NULL,
                time_architecture TEXT,
                character_schedules TEXT,
                world_background TEXT,
                narrative_rules TEXT,
                created_time TEXT NOT NULL,
                updated_time TEXT NOT NULL,
                UNIQUE(agent_name)
            )`,

            // 世界树心理状态表
            `CREATE TABLE IF NOT EXISTS worldtree_psychology_states (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                agent_name TEXT NOT NULL,
                psychology_data TEXT NOT NULL, -- JSON格式的心理状态数据
                emotion_factors TEXT, -- JSON格式的情绪因子
                context_summary TEXT, -- 上下文摘要
                last_updated TEXT NOT NULL, -- 最后更新时间
                created_time TEXT NOT NULL, -- 创建时间
                UNIQUE(user_id, agent_name)
            )`,

            // 世界树心理独白表（专门存储心理活动内容）
            `CREATE TABLE IF NOT EXISTS worldtree_psychology_monologues (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                agent_name TEXT NOT NULL,
                monologue_content TEXT NOT NULL, -- 心理独白内容
                psychology_state TEXT NOT NULL, -- JSON格式的当时心理状态
                context_data TEXT, -- JSON格式的上下文信息
                generation_method TEXT DEFAULT 'api', -- 生成方式：api/local
                quality_score REAL DEFAULT 0.0, -- 质量评分
                created_time TEXT NOT NULL -- 创建时间
            )`,

            // 兼容表：前端查询使用的表名
            `CREATE TABLE IF NOT EXISTS psychology_monologue (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                agent_name TEXT NOT NULL,
                content TEXT NOT NULL, -- 心理独白内容
                psychology_state TEXT, -- JSON格式的当时心理状态
                context_data TEXT, -- JSON格式的上下文信息
                generation_method TEXT DEFAULT 'api', -- 生成方式：api/local
                created_time TEXT NOT NULL -- 创建时间
            )`,

            // 世界树对话上下文表（专门存储对话历史）
            `CREATE TABLE IF NOT EXISTS worldtree_conversation_context (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                agent_name TEXT NOT NULL,
                conversation_data TEXT NOT NULL, -- JSON格式的对话内容
                speaker_role TEXT NOT NULL, -- user/assistant
                message_type TEXT DEFAULT 'normal', -- normal/system/psychology
                importance_score REAL DEFAULT 0.5, -- 重要性评分
                created_time TEXT NOT NULL -- 创建时间
            )`
        ];

        for (const table of tables) {
            await this.dbRun(table);
        }

        // 创建世界树专用索引
        const worldTreeIndexes = [
            `CREATE INDEX IF NOT EXISTS idx_worldtree_monologues_user_agent_time
             ON worldtree_psychology_monologues(user_id, agent_name, created_time)`,
            `CREATE INDEX IF NOT EXISTS idx_worldtree_context_user_agent_time
             ON worldtree_conversation_context(user_id, agent_name, created_time)`
        ];

        for (const index of worldTreeIndexes) {
            try {
                await this.dbRun(index);
            } catch (error) {
                this.logger.warning('世界树VCP', `创建索引失败: ${error.message}`);
            }
        }

        // 检查现有表是否存在
        try {
            await this.dbAll(`SELECT COUNT(*) FROM world_tree_states LIMIT 1`);
            this.logger.info('世界树VCP', '✅ 使用现有的world_tree_states表');
        } catch (error) {
            this.logger.warning('世界树VCP', 'world_tree_states表不存在，将创建');
            await this.dbRun(`
                CREATE TABLE IF NOT EXISTS world_tree_states (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    persona_name TEXT NOT NULL,
                    current_branch TEXT,
                    narrative_context TEXT,
                    world_state TEXT,
                    character_role TEXT,
                    story_progression REAL DEFAULT 0.0,
                    background_influence TEXT,
                    timestamp TEXT NOT NULL,
                    UNIQUE(user_id, persona_name)
                )
            `);
        }

        // 检查memory_fragments表（用于存储心理活动）
        try {
            await this.dbAll(`SELECT COUNT(*) FROM memory_fragments LIMIT 1`);
            this.logger.info('世界树VCP', '✅ 使用现有的memory_fragments表存储心理活动');
        } catch (error) {
            this.logger.error('世界树VCP', '❌ memory_fragments表不存在，无法存储心理活动');
        }

        // 创建索引（只为存在的表创建）
        const indexes = [
            `CREATE INDEX IF NOT EXISTS idx_world_tree_configs_agent
             ON world_tree_configs(agent_name)`,
            `CREATE INDEX IF NOT EXISTS idx_memory_fragments_psychology
             ON memory_fragments(user_id, persona_name, memory_type) WHERE memory_type = 'psychology_activity'`
        ];

        for (const index of indexes) {
            try {
                await this.dbRun(index);
            } catch (error) {
                this.logger.debug('世界树VCP', `索引创建跳过: ${error.message}`);
            }
        }
    }

    /**
     * 加载插件配置，优先使用插件自己的config.env
     */
    loadPluginConfig() {
        const pluginConfigPath = path.join(__dirname, 'config.env');

        let pluginConfig = {};

        // 加载插件配置
        try {
            const configContent = fsSync.readFileSync(pluginConfigPath, 'utf8');
            pluginConfig = this.parseEnvConfig(configContent);
            // 如果有logger，记录加载信息
            if (this.logger) {
                this.logger.info('世界树VCP', `从插件config.env加载了 ${Object.keys(pluginConfig).length} 个配置项`);
            }
        } catch (error) {
            // 插件配置不存在时，使用主服务器配置作为备用
            if (this.logger) {
                this.logger.info('世界树VCP', '插件config.env不存在或读取失败，使用主服务器配置作为备用');
            }
        }

        // 加载主服务器配置作为备用
        try {
            dotenv.config({ path: path.resolve(__dirname, '../../config.env') });
        } catch (error) {
            // 主配置文件不存在也没关系，使用默认值
        }

        // 配置优先级：插件配置 > 主服务器环境变量 > 默认值
        const mergedConfig = {
            enabled: this.getConfigValue(pluginConfig.WORLDTREE_ENABLED, 'true').toLowerCase() === 'true',
            apiUrl: this.getConfigValue(
                pluginConfig.WORLDTREE_API_URL,
                process.env.API_URL || process.env.OPENAI_TOOLS_URL,
                'https://yuanplus.cloud'
            ),
            apiKey: this.getConfigValue(
                pluginConfig.WORLDTREE_API_KEY,
                process.env.API_Key || process.env.OPENAI_TOOLS_KEY,
                ''
            ),
            model: this.getConfigValue(
                pluginConfig.WORLDTREE_MODEL,
                process.env.OPENAI_TOOLS_MODEL,
                'gpt-4o-mini'
            ),
            useLocalAlgorithm: this.getConfigValue(
                pluginConfig.WORLDTREE_USE_LOCAL_ALGORITHM,
                'false'
            ).toLowerCase() === 'true',
            forceAPIGeneration: this.getConfigValue(
                pluginConfig.WORLDTREE_FORCE_API_GENERATION,
                'false'
            ).toLowerCase() === 'true',
            maxCacheAge: parseInt(this.getConfigValue(
                pluginConfig.WORLDTREE_MAX_CACHE_AGE,
                '1800000' // 30分钟
            )),
            timeout: parseInt(this.getConfigValue(
                pluginConfig.WORLDTREE_TIMEOUT,
                '120000'
            )) || 120000,
            psychologyUpdateInterval: parseInt(this.getConfigValue(
                pluginConfig.WORLDTREE_PSYCHOLOGY_UPDATE_INTERVAL,
                '1800000'
            )) || 1800000, // 30分钟 - 内心独白更新间隔
            physicalStateUpdateInterval: parseInt(this.getConfigValue(
                pluginConfig.WORLDTREE_PHYSICAL_UPDATE_INTERVAL,
                '30000'
            )) || 30000, // 30秒 - 物理状态实时更新间隔
            contextMessagesCount: parseInt(this.getConfigValue(
                pluginConfig.WORLDTREE_CONTEXT_MESSAGES_COUNT,
                '6'
            )) || 6, // 对话上下文记录数量
            timeFormat: this.getConfigValue(
                pluginConfig.WORLDTREE_TIME_FORMAT,
                'local'
            ),
            debugMode: this.getConfigValue(
                pluginConfig.WORLDTREE_DEBUG_MODE,
                'false'
            ).toLowerCase() === 'true',
            logLevel: this.getConfigValue(
                pluginConfig.WORLDTREE_LOG_LEVEL,
                'info'
            ),
            // 显示配置
            showEnergy: this.getConfigValue(
                pluginConfig.WORLDTREE_SHOW_ENERGY,
                'true'
            ).toLowerCase() === 'true',
            showPsychologyDetails: this.getConfigValue(
                pluginConfig.WORLDTREE_SHOW_PSYCHOLOGY_DETAILS,
                'true'
            ).toLowerCase() === 'true',
            showTimeArchitecture: this.getConfigValue(
                pluginConfig.WORLDTREE_SHOW_TIME_ARCHITECTURE,
                'true'
            ).toLowerCase() === 'true',
            showCharacterSchedules: this.getConfigValue(
                pluginConfig.WORLDTREE_SHOW_CHARACTER_SCHEDULES,
                'true'
            ).toLowerCase() === 'true'
        };

        return mergedConfig;
    }

    /**
     * 获取配置值，支持优先级
     */
    getConfigValue(pluginValue, mainValue, defaultValue = '') {
        return (pluginValue && pluginValue.trim()) ||
               (mainValue && mainValue.trim()) ||
               defaultValue;
    }

    /**
     * 解析.env格式的配置文件
     */
    parseEnvConfig(content) {
        const config = {};
        content.split('\n').forEach(line => {
            line = line.trim();
            if (line && !line.startsWith('#')) {
                const equalIndex = line.indexOf('=');
                if (equalIndex > 0) {
                    const key = line.substring(0, equalIndex).trim();
                    const value = line.substring(equalIndex + 1).trim().replace(/^["']|["']$/g, '');
                    config[key] = value;
                }
            }
        });
        return config;
    }

    /**
     * 获取默认配置
     */
    getDefaultConfig() {
        return {
            enabled: true,
            apiUrl: 'https://yuanplus.cloud',
            apiKey: '',
            model: 'gpt-4o-mini',
            useLocalAlgorithm: true,
            timeout: 120000,
            psychologyUpdateInterval: 1800000, // 30分钟 - 内心独白更新
            physicalStateUpdateInterval: 30000, // 30秒 - 物理状态更新
            contextMessagesCount: 6, // 对话上下文记录数量
            timeFormat: 'local',
            debugMode: false,
            logLevel: 'info',
            // 显示配置
            showEnergy: true,
            showPsychologyDetails: true,
            showTimeArchitecture: true,
            showCharacterSchedules: true
        };
    }

    /**
     * 启动心理状态更新定时器（分离内心独白和物理状态）
     */
    startPsychologyUpdateTimer() {
        // 停止现有定时器
        if (this.psychologyTimer) {
            clearInterval(this.psychologyTimer);
        }
        if (this.physicalStateTimer) {
            clearInterval(this.physicalStateTimer);
        }

        // 启动内心独白更新定时器（30分钟）
        this.psychologyTimer = setInterval(async () => {
            try {
                await this.updateAllPsychologyMonologues();
            } catch (error) {
                this.logger.error('世界树VCP', '内心独白更新失败:', error.message);
            }
        }, this.config.psychologyUpdateInterval);

        // 启动物理状态更新定时器（30秒）
        this.physicalStateTimer = setInterval(async () => {
            try {
                await this.updateAllPhysicalStates();
            } catch (error) {
                this.logger.error('世界树VCP', '物理状态更新失败:', error.message);
            }
        }, this.config.physicalStateUpdateInterval);

        this.logger.info('世界树VCP', `内心独白更新定时器已启动，间隔: ${this.config.psychologyUpdateInterval / 1000}秒`);
        this.logger.info('世界树VCP', `物理状态更新定时器已启动，间隔: ${this.config.physicalStateUpdateInterval / 1000}秒`);
    }

    /**
     * 获取当前本地时间
     */
    getCurrentLocalTime() {
        const now = new Date();
        return now.toLocaleString('zh-CN', {
            timeZone: 'Asia/Shanghai',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    /**
     * 分析对话情感内容
     */
    analyzeConversationEmotions(conversations) {
        if (!conversations || conversations.length === 0) {
            return { valence: 0, arousal: 0, dominance: 0, confidence: 0 };
        }

        // 合并最近的对话内容
        const combinedText = conversations.slice(0, 5).map(conv => conv.content).join(' ');

        // 使用情感计算模块分析
        return this.emotionalComputing.analyzeTextEmotion(combinedText);
    }

    /**
     * 估算距离上次用餐时间
     */
    estimateTimeSinceLastMeal() {
        const now = moment();
        const hour = now.hour();

        // 基于常见用餐时间估算
        const mealTimes = [7, 12, 18]; // 早餐、午餐、晚餐
        const timeDiffs = mealTimes.map(mealTime => {
            let diff = hour - mealTime;
            if (diff < 0) diff += 24; // 处理跨天情况
            return diff;
        });

        return Math.min(...timeDiffs);
    }

    /**
     * 估算距离上次睡眠时间
     */
    estimateTimeSinceLastSleep() {
        const now = moment();
        const hour = now.hour();

        // 假设正常睡眠时间为23:00-7:00
        if (hour >= 7 && hour < 23) {
            return hour - 7; // 从早上7点开始计算
        } else {
            return hour + 1; // 夜间时段
        }
    }

    /**
     * 估算任务复杂度
     */
    estimateTaskComplexity(fullContext) {
        let complexity = 0.3; // 基础复杂度

        // 基于对话长度
        if (fullContext.recentConversations.length > 0) {
            const avgLength = fullContext.recentConversations.reduce((sum, conv) =>
                sum + conv.content.length, 0) / fullContext.recentConversations.length;
            complexity += Math.min(0.4, avgLength / 200); // 长对话增加复杂度
        }

        // 基于系统提示词复杂度
        if (fullContext.systemPrompt) {
            complexity += Math.min(0.3, fullContext.systemPrompt.length / 1000);
        }

        return Math.min(1.0, complexity);
    }

    /**
     * 应用情感认知效应
     */
    applyEmotionalCognitiveEffects(scientificState, emotionalEffects) {
        return {
            ...scientificState,
            focus: scientificState.focus * emotionalEffects.attentionFocus,
            energy: scientificState.energy * emotionalEffects.cognitiveEfficiency,
            alertness: scientificState.alertness * (1 + emotionalEffects.socialMotivation * 0.1),
            mood: scientificState.mood * (1 + emotionalEffects.memoryBias * 0.2)
        };
    }

    /**
     * 应用个性化调整
     */
    async applyPersonalization(state, agentName, fullContext) {
        try {
            // 获取Agent的世界树配置
            const worldTreeConfig = await this.getWorldTreeConfig(agentName);

            // 基于配置调整状态
            if (worldTreeConfig) {
                // 根据角色特点调整
                if (worldTreeConfig.worldBackground) {
                    const background = worldTreeConfig.worldBackground.toLowerCase();
                    if (background.includes('研究') || background.includes('技术')) {
                        state.focus *= 1.1; // 研究型角色专注度加成
                    }
                    if (background.includes('活泼') || background.includes('开朗')) {
                        state.energy *= 1.1; // 活泼角色精力加成
                    }
                }
            }

            return state;
        } catch (error) {
            this.logger.warning('世界树VCP', '个性化调整失败:', error.message);
            return state;
        }
    }

    /**
     * 获取默认心理状态
     */
    getDefaultPsychologyState() {
        return {
            focus: 50,
            energy: 50,
            fatigue: 30,
            alertness: 60,
            hunger: 40,
            stress: 30,
            mood: 50,
            timePeriod: this.getTimePeriod(),
            timestamp: this.getCurrentLocalTime(),
            algorithm: 'default_fallback'
        };
    }

    /**
     * 获取缓存的心理活动
     */
    async getCachedPsychologyActivity(userId, agentName) {
        try {
            // 🎯 统一使用世界树专用表获取心理活动
            const recentMonologue = await this.dbGet(`
                SELECT monologue_content, psychology_state, context_data, created_time, generation_method
                FROM worldtree_psychology_monologues
                WHERE user_id = ? AND agent_name = ?
                ORDER BY created_time DESC
                LIMIT 1
            `, [userId, agentName]);

            if (recentMonologue) {
                return {
                    content: recentMonologue.monologue_content,
                    psychologyState: JSON.parse(recentMonologue.psychology_state || '{}'),
                    contextData: JSON.parse(recentMonologue.context_data || '{}'),
                    timestamp: recentMonologue.created_time,
                    generationMethod: recentMonologue.generation_method,
                    cached: true,
                    source: 'worldtree_monologue'
                };
            }

            // 如果没有心理独白，返回null让系统生成新的
            return null;
        } catch (error) {
            this.logger.warning('世界树VCP', '获取缓存心理活动失败:', error.message);
            return {
                psychologyState: this.getDefaultPsychologyState(),
                content: '当前状态平稳，保持正常的生理心理水平。',
                timestamp: this.getCurrentLocalTime(),
                cached: false
            };
        }
    }

    /**
     * 生成对话指导建议
     */
    generateConversationalGuidance(psychologyState) {
        try {
            // 使用集成分析器分析对话影响
            const integratedAnalysis = this.integratedAnalyzer.analyzeIntegratedPsychology(
                psychologyState,
                { achievement: 0.6, novelty: 0.4, uncertainty: 0.3 },
                { affinity: 0.8, relationshipType: 'friend' }
            );

            const conversational = integratedAnalysis.conversationalImpact;

            let guidance = '\n[当前状态对对话的影响分析]\n';

            // 对话能力评估
            guidance += `• 对话能力: ${conversational.capacity}% - `;
            if (conversational.capacity > 70) {
                guidance += '状态良好，可进行深度交流\n';
            } else if (conversational.capacity > 40) {
                guidance += '状态一般，建议适度交流\n';
            } else {
                guidance += '状态较差，建议简短交流\n';
            }

            // 表达倾向分析
            guidance += `• 表达倾向: ${conversational.expression.verbosity}详细度, ${conversational.expression.depth}深度, ${conversational.expression.emotional}情感控制\n`;

            // 理解能力分析
            guidance += `• 理解能力: ${conversational.comprehension.level}水平, ${conversational.comprehension.processing}处理方式, ${conversational.comprehension.context}上下文理解\n`;

            // 情感共鸣能力
            guidance += `• 共鸣能力: 认知${conversational.empathy.cognitive}/情感${conversational.empathy.affective}/同情${conversational.empathy.compassionate}\n`;

            // 推荐对话策略
            guidance += `• 推荐策略: ${conversational.strategy}\n`;

            // 具体建议
            guidance += `• 对话建议:\n`;
            conversational.recommendations.forEach(rec => {
                guidance += `  - ${rec}\n`;
            });

            // 基于物理状态的额外建议
            const { focus, energy, fatigue, alertness } = psychologyState;

            guidance += `• 状态适应建议:\n`;
            if (fatigue > 80) {
                guidance += `  - 疲劳度${fatigue.toFixed(1)}%，建议简化表达，避免长篇回复\n`;
            }
            if (energy < 20) {
                guidance += `  - 精力${energy.toFixed(1)}%，建议保存体力，重点回应核心问题\n`;
            }
            if (focus > 80) {
                guidance += `  - 专注度${focus.toFixed(1)}%，可进行深度技术讨论和复杂分析\n`;
            }
            if (alertness < 40) {
                guidance += `  - 警觉性${alertness.toFixed(1)}%，反应可能较慢，需要更多思考时间\n`;
            }

            guidance += '---\n';

            return guidance;
        } catch (error) {
            this.logger.warning('世界树VCP', '对话指导生成失败:', error.message);
            return '';
        }
    }

    /**
     * 获取完整的心理活动上下文信息
     */
    async getFullContextForPsychology(userId, agentName, contextFactors = {}) {
        try {
            const context = {
                ...contextFactors,
                timestamp: this.getCurrentLocalTime(),
                timePeriod: this.getTimePeriod(),
                recentConversations: [],
                systemPrompt: null,
                emotionalState: null,
                memoryFragments: []
            };

            // 获取最近的对话记录（从conversation_history表）
            try {
                const recentChats = await this.dbAll(`
                    SELECT content, speaker, timestamp, emotion_state
                    FROM conversation_history
                    WHERE user_id = ? AND persona_name = ?
                    ORDER BY timestamp DESC
                    LIMIT 10
                `, [userId, agentName]);

                context.recentConversations = recentChats || [];
            } catch (error) {
                this.logger.debug('世界树VCP', '未找到对话记录表，使用空数据');
                context.recentConversations = [];
            }

            // 获取当前的情感状态（从user_affinity表）
            try {
                const emotionData = await this.dbAll(`
                    SELECT emotion_valence, emotion_arousal, emotion_dominance,
                           current_affinity, relationship_type
                    FROM user_affinity
                    WHERE user_id = ? AND persona_name = ?
                    LIMIT 1
                `, [userId, agentName]);

                context.emotionalState = emotionData[0] || null;
            } catch (error) {
                this.logger.debug('世界树VCP', '未找到情感状态数据');
                context.emotionalState = null;
            }

            // 获取相关的记忆片段（从memory_fragments表）
            try {
                const memories = await this.dbAll(`
                    SELECT content, ai_summary, memory_type, importance_score,
                           emotional_context, creation_time
                    FROM memory_fragments
                    WHERE user_id = ? AND persona_name = ?
                    ORDER BY importance_score DESC, creation_time DESC
                    LIMIT 5
                `, [userId, agentName]);

                context.memoryFragments = memories || [];
            } catch (error) {
                this.logger.debug('世界树VCP', '未找到记忆片段数据');
                context.memoryFragments = [];
            }

            // 获取Agent的系统提示词（从Agent文件）
            try {
                const agentFilePath = path.join(__dirname, '../../Agent', `${agentName}.txt`);
                const agentContent = await fs.readFile(agentFilePath, 'utf-8');
                context.systemPrompt = agentContent.trim();
            } catch (error) {
                this.logger.debug('世界树VCP', `未找到Agent文件: ${agentName}.txt`);
                context.systemPrompt = null;
            }

            return context;
        } catch (error) {
            this.logger.error('世界树VCP', '获取完整上下文失败:', error.message);
            return {
                ...contextFactors,
                timestamp: this.getCurrentLocalTime(),
                timePeriod: this.getTimePeriod(),
                recentConversations: [],
                systemPrompt: null,
                emotionalState: null,
                memoryFragments: []
            };
        }
    }

    /**
     * 获取时间段
     */
    getTimePeriod() {
        const hour = new Date().getHours();
        if (hour >= 6 && hour < 12) return 'morning';
        if (hour >= 12 && hour < 18) return 'afternoon';
        if (hour >= 18 && hour < 22) return 'evening';
        return 'night';
    }

    /**
     * 创建或更新世界树配置
     */
    async createOrUpdateWorldTreeConfig(agentName, configData) {
        try {
            const currentTime = this.getCurrentLocalTime();

            const existingConfig = await this.dbGet(
                'SELECT * FROM world_tree_configs WHERE agent_name = ?',
                [agentName]
            );

            if (existingConfig) {
                // 更新现有配置
                await this.dbRun(`
                    UPDATE world_tree_configs
                    SET config_data = ?,
                        time_architecture = ?,
                        character_schedules = ?,
                        world_background = ?,
                        narrative_rules = ?,
                        updated_time = ?
                    WHERE agent_name = ?
                `, [
                    JSON.stringify(configData.config || {}),
                    JSON.stringify(configData.timeArchitecture || {}),
                    JSON.stringify(configData.characterSchedules || {}),
                    configData.worldBackground || '',
                    JSON.stringify(configData.narrativeRules || {}),
                    currentTime,
                    agentName
                ]);

                this.logger.info('世界树VCP', `更新Agent配置: ${agentName}`);
            } else {
                // 创建新配置
                await this.dbRun(`
                    INSERT INTO world_tree_configs
                    (agent_name, config_data, time_architecture, character_schedules, world_background, narrative_rules, created_time, updated_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                    agentName,
                    JSON.stringify(configData.config || {}),
                    JSON.stringify(configData.timeArchitecture || {}),
                    JSON.stringify(configData.characterSchedules || {}),
                    configData.worldBackground || '',
                    JSON.stringify(configData.narrativeRules || {}),
                    currentTime,
                    currentTime
                ]);

                this.logger.info('世界树VCP', `创建Agent配置: ${agentName}`);
            }

            // 更新内存缓存（深拷贝以避免引用问题）
            const cachedConfigData = {
                config: JSON.parse(JSON.stringify(configData.config || {})),
                timeArchitecture: JSON.parse(JSON.stringify(configData.timeArchitecture || {})),
                characterSchedules: JSON.parse(JSON.stringify(configData.characterSchedules || {})),
                worldBackground: configData.worldBackground || '',
                narrativeRules: JSON.parse(JSON.stringify(configData.narrativeRules || {})),
                createdTime: existingConfig ? existingConfig.created_time : currentTime,
                updatedTime: currentTime
            };

            this.worldTreeConfigs.set(agentName, cachedConfigData);
            this.configCacheTimestamps.set(agentName, Date.now());

            // 记录配置更新日志
            this.logger.debug('世界树VCP', `配置缓存已更新: ${agentName}`, {
                hasConfig: !!cachedConfigData.config,
                hasTimeArchitecture: !!cachedConfigData.timeArchitecture,
                hasCharacterSchedules: !!cachedConfigData.characterSchedules,
                hasWorldBackground: !!cachedConfigData.worldBackground,
                hasNarrativeRules: !!cachedConfigData.narrativeRules
            });

            return true;
        } catch (error) {
            this.logger.error('世界树VCP', `配置保存失败 [${agentName}]:`, error.message);
            return false;
        }
    }

    /**
     * 获取世界树配置
     */
    async getWorldTreeConfig(agentName) {
        try {
            // 先检查内存缓存
            if (this.worldTreeConfigs.has(agentName)) {
                const cacheTimestamp = this.configCacheTimestamps.get(agentName) || 0;
                const cacheAge = Date.now() - cacheTimestamp;
                const maxCacheAge = this.config?.maxCacheAge || 1800000; // 30分钟

                // 检查缓存是否过期
                if (cacheAge > maxCacheAge) {
                    this.logger.debug('世界树VCP', `配置缓存已过期，重新加载: ${agentName}`, {
                        cacheAge: Math.round(cacheAge / 1000),
                        maxCacheAge: Math.round(maxCacheAge / 1000)
                    });
                    this.worldTreeConfigs.delete(agentName);
                    this.configCacheTimestamps.delete(agentName);
                } else {
                    const cachedConfig = this.worldTreeConfigs.get(agentName);
                    // 返回深拷贝以避免外部修改影响缓存
                    return {
                        config: JSON.parse(JSON.stringify(cachedConfig.config || {})),
                        timeArchitecture: JSON.parse(JSON.stringify(cachedConfig.timeArchitecture || {})),
                        characterSchedules: JSON.parse(JSON.stringify(cachedConfig.characterSchedules || {})),
                        worldBackground: cachedConfig.worldBackground || '',
                        narrativeRules: JSON.parse(JSON.stringify(cachedConfig.narrativeRules || {})),
                        createdTime: cachedConfig.createdTime,
                        updatedTime: cachedConfig.updatedTime
                    };
                }
            }

            // 从数据库加载
            const config = await this.dbGet(
                'SELECT * FROM world_tree_configs WHERE agent_name = ?',
                [agentName]
            );

            if (config) {
                const configData = {
                    config: JSON.parse(config.config_data || '{}'),
                    timeArchitecture: JSON.parse(config.time_architecture || '{}'),
                    characterSchedules: JSON.parse(config.character_schedules || '{}'),
                    worldBackground: config.world_background || '',
                    narrativeRules: JSON.parse(config.narrative_rules || '{}'),
                    createdTime: config.created_time,
                    updatedTime: config.updated_time
                };

                // 更新内存缓存
                this.worldTreeConfigs.set(agentName, configData);
                this.configCacheTimestamps.set(agentName, Date.now());

                // 返回深拷贝
                return {
                    config: JSON.parse(JSON.stringify(configData.config || {})),
                    timeArchitecture: JSON.parse(JSON.stringify(configData.timeArchitecture || {})),
                    characterSchedules: JSON.parse(JSON.stringify(configData.characterSchedules || {})),
                    worldBackground: configData.worldBackground || '',
                    narrativeRules: JSON.parse(JSON.stringify(configData.narrativeRules || {})),
                    createdTime: configData.createdTime,
                    updatedTime: configData.updatedTime
                };
            }

            return null;
        } catch (error) {
            this.logger.error('世界树VCP', `配置加载失败 [${agentName}]:`, error.message);
            // 清除可能损坏的缓存
            if (this.worldTreeConfigs.has(agentName)) {
                this.worldTreeConfigs.delete(agentName);
                this.configCacheTimestamps.delete(agentName);
                this.logger.warning('世界树VCP', `已清除损坏的配置缓存: ${agentName}`);
            }
            return null;
        }
    }

    /**
     * 生成心理活动内容（集成到emotion_memory.db）
     */
    async generatePsychologyActivity(userId, agentName, contextFactors = {}) {
        try {
            const now = Date.now();
            const isRequestTriggered = contextFactors.isRequestTriggered || false;
            const autoUpdateInterval = this.config.psychologyUpdateInterval || 1800000; // 30分钟

            // 🔧 修复更新逻辑：区分不同的更新类型
            const isScheduledUpdate = contextFactors.updateType === 'scheduled_monologue';
            const isConversationTriggered = contextFactors.updateType === 'conversation_triggered';
            const isApiRequest = contextFactors.updateType === 'api_request';

            // 🔍 详细调试日志 - 每次调用都记录
            const callId = Math.random().toString(36).substr(2, 9);
            this.logger.info('世界树VCP', `🎯 心理分析调用开始 [${callId}] [${agentName}]:`, {
                userId,
                isRequestTriggered,
                isScheduledUpdate,
                isConversationTriggered,
                isApiRequest,
                updateType: contextFactors.updateType,
                hasRecentConversation: contextFactors.hasRecentConversation,
                timeSinceLastUpdate: now - this.lastAutoUpdateTime,
                timestamp: new Date().toISOString(),
                contextFactors: contextFactors // 添加完整的 contextFactors 用于调试
            });

            // 🔧 彻底分离：对话触发、API请求、系统消息生成和主程序请求无任何限制，立即执行
            const isSystemMessageGeneration = contextFactors.updateType === 'system_message_generation';

            if (isRequestTriggered || isConversationTriggered || isApiRequest || isSystemMessageGeneration) {
                // 对话触发、API请求、系统消息生成和主程序请求：无CD限制，每次都执行
                if (isRequestTriggered) {
                    this.logger.info('世界树VCP', `✅ 主程序请求触发，立即执行: ${agentName}`);
                } else if (isApiRequest) {
                    this.logger.info('世界树VCP', `✅ API请求触发，立即执行: ${agentName}`);
                } else if (isSystemMessageGeneration) {
                    this.logger.info('世界树VCP', `✅ 系统消息生成触发，立即执行: ${agentName}`);
                } else {
                    this.logger.info('世界树VCP', `✅ 对话触发，立即执行: ${agentName}`);
                }
                // 不更新任何时间戳，保持独立性
            } else if (isScheduledUpdate) {
                // 定时更新：强制执行，更新定时器时间戳
                this.logger.info('世界树VCP', `✅ 定时更新触发，强制执行: ${agentName}`);
                // 定时更新可以更新自己的时间戳，但不影响对话触发
            } else {
                // 普通调用：检查时间间隔
                const timeSinceLastUpdate = now - this.lastAutoUpdateTime;
                this.logger.debug('世界树VCP', `⏰ 时间检查: 当前时间=${now}, 上次更新=${this.lastAutoUpdateTime}, 间隔=${Math.round(timeSinceLastUpdate/1000)}s, 要求间隔=${autoUpdateInterval/1000}s`);

                if (timeSinceLastUpdate < autoUpdateInterval) {
                    this.logger.debug('世界树VCP', `⏰ 普通调用未到更新时间 (${Math.round(timeSinceLastUpdate/1000)}s < ${autoUpdateInterval/1000}s)，返回缓存: ${agentName}`);
                    return await this.getCachedPsychologyActivity(userId, agentName);
                }

                // 更新时间戳
                this.lastAutoUpdateTime = now;
                this.logger.info('世界树VCP', `✅ 普通调用触发，时间间隔满足 (${Math.round(timeSinceLastUpdate/1000)}s >= ${autoUpdateInterval/1000}s): ${agentName}`);
            }

            // 获取现有的心理状态数据
            const psychologyState = await this.calculatePsychologyState(userId, agentName, contextFactors);

            // 获取世界树配置
            const worldTreeConfig = await this.getWorldTreeConfig(agentName);

            // 获取完整的上下文信息
            const fullContext = await this.getFullContextForPsychology(userId, agentName, contextFactors);

            // 🚀 新的逻辑：每次都异步调用API，立即返回数据库最新数据

            // 1. 立即从数据库获取最新的心理独白
            const latestActivity = await this.getLatestPsychologyActivity(userId, agentName);

            let psychologyContent = '';
            let dataSource = 'database';

            if (latestActivity && latestActivity.content) {
                // 使用数据库中最新的内容
                psychologyContent = latestActivity.content;
                this.logger.info('世界树VCP', `使用数据库最新心理独白: ${agentName} (${latestActivity.generationMethod})`);
            } else {
                // 如果数据库没有数据，使用本地算法生成临时内容
                psychologyContent = await this.generateLocalPsychologyContent(psychologyState, worldTreeConfig, {});
                dataSource = 'local_fallback';
                this.logger.info('世界树VCP', `数据库无数据，使用本地生成: ${agentName}`);

                // 保存本地生成的内容到数据库
                await this.saveMonologueToWorldTree(
                    userId,
                    agentName,
                    psychologyContent,
                    psychologyState,
                    fullContext,
                    'local'
                );
            }

            // 2. 🚀 强制异步触发API生成新的心理独白（无任何限制）
            this.logger.info('世界树VCP', `🚀 强制异步触发API生成: ${agentName} (触发类型: ${isConversationTriggered ? '对话' : isRequestTriggered ? '主程序' : '其他'})`);

            // 获取上一次独白作为参考（如果有的话）
            const lastMonologue = contextFactors.lastMonologue || await this.getLastMonologue(agentName);
            this.logger.debug('世界树VCP', `获取上一次独白: ${agentName} - ${lastMonologue ? '成功' : '无历史记录'}`);

            // 🔧 强制异步调用，确保每次都执行
            const apiStartTime = Date.now();
            this.logger.info('世界树VCP', `🎯 开始异步API调用 [${callId}]: ${agentName}`);

            this.generateIntelligentPsychologyContentAsync(
                userId,
                agentName,
                psychologyState,
                worldTreeConfig,
                fullContext,
                lastMonologue // 传入上一次独白作为参考
            ).then(result => {
                const apiEndTime = Date.now();
                const apiDuration = (apiEndTime - apiStartTime) / 1000;
                this.logger.info('世界树VCP', `✅ 异步API生成完成 [${callId}] [${agentName}]，耗时: ${apiDuration.toFixed(2)}秒，结果: ${result ? '成功' : '无结果'}`);

                // 如果是对话触发，记录成功
                if (isConversationTriggered) {
                    this.logger.info('世界树VCP', `🎊 对话触发的心理独白生成成功 [${callId}]: ${agentName}`);
                }
            }).catch(error => {
                const apiEndTime = Date.now();
                const apiDuration = (apiEndTime - apiStartTime) / 1000;
                this.logger.error('世界树VCP', `❌ 异步API生成失败 [${callId}] [${agentName}]，耗时: ${apiDuration.toFixed(2)}秒，错误: ${error.message}`);
                this.logger.error('世界树VCP', `错误堆栈: ${error.stack}`);
            });

            return {
                psychologyState,
                content: psychologyContent,
                timestamp: this.getCurrentLocalTime(),
                source: dataSource
            };

        } catch (error) {
            this.logger.error('世界树VCP', `心理活动生成失败 [${userId}/${agentName}]:`, error.message);
            return null;
        }
    }

    /**
     * 计算心理状态（使用科学算法）
     */
    async calculatePsychologyState(userId, agentName, contextFactors = {}) {
        try {
            // 获取完整上下文
            const fullContext = await this.getFullContextForPsychology(userId, agentName, contextFactors);

            // 使用时间生物学模块计算时间相关因素
            const chronobiologyFactors = this.chronobiology.getTimeBasedPsychologyFactors();

            // 分析最近对话的情感内容
            const emotionalAnalysis = this.analyzeConversationEmotions(fullContext.recentConversations);

            // 更新情感计算模块状态
            this.emotionalComputing.updateEmotionalState(emotionalAnalysis, {
                socialInteraction: fullContext.recentConversations.length > 0 ? 0.8 : 0.2,
                achievement: contextFactors.achievement || 0.5,
                novelty: contextFactors.novelty || 0.3
            });

            // 构建科学算法的输入参数
            const scientificContextFactors = {
                // 时间相关
                ...chronobiologyFactors,

                // 生理相关
                timeSinceLastMeal: contextFactors.timeSinceLastMeal || this.estimateTimeSinceLastMeal(),
                timeSinceLastSleep: contextFactors.timeSinceLastSleep || this.estimateTimeSinceLastSleep(),
                physicalActivity: contextFactors.physicalActivity || 0.3,

                // 认知相关
                recentInteractions: fullContext.recentConversations.length,
                taskComplexity: this.estimateTaskComplexity(fullContext),
                multitasking: contextFactors.multitasking || 0.2,

                // 情感相关
                socialInteraction: fullContext.recentConversations.length > 0 ? 0.8 : 0.2,
                achievement: contextFactors.achievement || 0.5,
                novelty: contextFactors.novelty || 0.3,
                uncertainty: contextFactors.uncertainty || 0.3,
                timePress: contextFactors.timePress || 0.3,

                // 情绪状态
                ...this.emotionalComputing.getCurrentEmotionalState().mood
            };

            // 🧠 选择算法：优先使用人性化本地算法
            let psychologyState;

            if (this.config.useLocalAlgorithm) { // 使用配置决定算法选择
                // 使用新的人性化本地算法
                psychologyState = await this.calculateLocalPsychologyState(fullContext, contextFactors);
            } else {
                // 使用科学算法计算心理状态
                const scientificState = this.scientificAlgorithms.calculatePsychologicalState(scientificContextFactors);

                // 获取情感对认知的影响
                const emotionalCognitiveEffects = this.emotionalComputing.getEmotionalCognitiveEffects();

                // 应用情感认知效应
                psychologyState = this.applyEmotionalCognitiveEffects(scientificState, emotionalCognitiveEffects);
            }

            // 应用个性化调整
            const personalizedState = await this.applyPersonalization(psychologyState, agentName, fullContext);

            return personalizedState;
        } catch (error) {
            this.logger.error('世界树VCP', '科学心理状态计算失败:', error.message);
            return this.getDefaultPsychologyState();
        }
    }

    /**
     * 使用本地人性化算法计算心理状态
     */
    async calculateLocalPsychologyState(fullContext, contextFactors = {}) {
        try {
            // 获取时间因子（增加动态性）
            const timeFactors = this.getTimeFactors();

            // 添加实时变化因子
            const realTimeFactors = {
                timeVariation: contextFactors.timeVariation || Math.sin(Date.now() / 60000) * 0.15,
                agentSpecificSeed: contextFactors.agentSpecificSeed || 0,
                cognitiveLoad: contextFactors.cognitiveLoad || Math.random() * 0.3 + 0.1,
                realTimeUpdate: contextFactors.realTimeUpdate || false,
                monitoringMode: contextFactors.monitoringMode || false
            };

            // 构建状态矩阵（包含动态因子）
            const stateMatrix = await this.buildStateMatrix(
                fullContext.recentConversations,
                { ...timeFactors, ...realTimeFactors },
                contextFactors
            );

            // 使用新的人性化算法计算物理属性
            const physicalAttributes = this.computePhysicalAttributes(stateMatrix, realTimeFactors);

            // 添加时间段信息
            physicalAttributes.timePeriod = this.getTimePeriod();

            // 添加情感记忆数据
            const emotionMemoryData = await this.getEmotionMemoryData(
                fullContext.userId || 'default',
                fullContext.agentName || 'default'
            );

            // 应用实时变化（如果是监控模式）
            if (realTimeFactors.monitoringMode) {
                const agentName = fullContext.agentName || 'default';
                const agentSeed = agentName.charCodeAt(0) / 100;
                const timeFactor = Math.sin(Date.now() / 120000 + agentSeed * 10) * 0.2;

                // 为每个Agent添加不同的变化模式
                physicalAttributes.focus += timeFactor * 15 + (Math.random() - 0.5) * 8;
                physicalAttributes.energy += timeFactor * 12 + (Math.random() - 0.5) * 10;
                physicalAttributes.fatigue += -timeFactor * 10 + (Math.random() - 0.5) * 12;
                physicalAttributes.alertness += timeFactor * 18 + (Math.random() - 0.5) * 15;
                physicalAttributes.hunger += Math.sin(Date.now() / 180000 + agentSeed * 5) * 8 + (Math.random() - 0.5) * 6;

                // 确保数值在合理范围内
                physicalAttributes.focus = Math.max(0, Math.min(100, physicalAttributes.focus));
                physicalAttributes.energy = Math.max(0, Math.min(100, physicalAttributes.energy));
                physicalAttributes.fatigue = Math.max(0, Math.min(100, physicalAttributes.fatigue));
                physicalAttributes.alertness = Math.max(0, Math.min(100, physicalAttributes.alertness));
                physicalAttributes.hunger = Math.max(0, Math.min(100, physicalAttributes.hunger));
            }

            // 合并情感数据
            return {
                ...physicalAttributes,
                stress: emotionMemoryData.stress || physicalAttributes.stress || 30,
                mood: emotionMemoryData.mood || physicalAttributes.mood || 50,
                emotion: emotionMemoryData.emotion || 0,
                affinity: emotionMemoryData.affinity || 50
            };

        } catch (error) {
            this.logger.error('世界树VCP', '本地心理状态计算失败:', error.message);
            return this.getDefaultPsychologyState();
        }
    }

    /**
     * 获取情感记忆数据
     */
    async getEmotionMemoryData(userId, agentName) {
        try {
            // 检查表是否存在并获取数据
            const tables = await this.dbAll(`
                SELECT name FROM sqlite_master
                WHERE type='table' AND (name='ai_stress_states' OR name='user_affinity' OR name='emotion_analysis')
            `);

            const existingTables = tables.map(t => t.name);
            let stressValue = 0;
            let emotionValue = 0;
            let moodValue = 0;
            let affinityValue = 50;

            // 尝试从不同的表获取数据
            if (existingTables.includes('ai_stress_states')) {
                const stressData = await this.dbGet(`
                    SELECT stress_value, timestamp
                    FROM ai_stress_states
                    WHERE user_id = ? AND persona_name = ?
                    ORDER BY timestamp DESC LIMIT 1
                `, [userId, agentName]);
                stressValue = stressData?.stress_value || 0;
            }

            if (existingTables.includes('emotion_analysis')) {
                const emotionData = await this.dbGet(`
                    SELECT emotion_valence, emotion_arousal, emotion_dominance
                    FROM emotion_analysis
                    WHERE user_id = ? AND persona_name = ?
                    ORDER BY timestamp DESC LIMIT 1
                `, [userId, agentName]);

                if (emotionData) {
                    emotionValue = emotionData.emotion_valence || 0;
                    moodValue = emotionData.emotion_arousal || 0;
                }
            }

            return {
                stress: stressValue,
                emotion: emotionValue,
                mood: moodValue,
                affinity: affinityValue,
                dominance: 0
            };

        } catch (error) {
            this.logger.warning('世界树VCP', `情感数据获取失败:`, error.message);
            return { stress: 0, emotion: 0, mood: 0, affinity: 50, dominance: 0 };
        }
    }

    /**
     * 获取最近对话记录
     */
    async getRecentConversations(userId, agentName, limit = 10) {
        try {
            // 尝试从不同的可能表中获取对话记录
            let conversations = [];

            // 首先尝试从recent_conversations表获取
            try {
                conversations = await this.dbAll(`
                    SELECT content, creation_time as timestamp, 'user' as role
                    FROM recent_conversations
                    WHERE user_id = ? AND persona_name = ?
                    ORDER BY creation_time DESC LIMIT ?
                `, [userId, agentName, limit]);
            } catch (e1) {
                // 如果失败，尝试从其他可能的表
                try {
                    conversations = await this.dbAll(`
                        SELECT message_content as content, timestamp, 'user' as role
                        FROM conversation_logs
                        WHERE user_id = ? AND agent_name = ?
                        ORDER BY timestamp DESC LIMIT ?
                    `, [userId, agentName, limit]);
                } catch (e2) {
                    this.logger.debug('世界树VCP', '未找到对话记录表，使用空数据');
                }
            }

            return conversations || [];
        } catch (error) {
            this.logger.warning('世界树VCP', '获取对话记录失败:', error.message);
            return [];
        }
    }

    /**
     * 构建状态计算矩阵
     */
    async buildStateMatrix(conversations, timeFactors, contextFactors) {
        // 分析对话内容的复杂度和情感倾向
        const conversationAnalysis = this.analyzeConversations(conversations);

        // 时间衰减函数
        const timeDecay = this.calculateTimeDecay(conversations);

        // 构建特征矩阵
        const features = [
            conversationAnalysis.complexity,      // 对话复杂度
            conversationAnalysis.frequency,       // 对话频率
            conversationAnalysis.length,          // 对话长度
            timeFactors.circadianRhythm,         // 生理节律
            timeFactors.timeOfDay,               // 时间因子
            contextFactors.cognitiveLoad || 0,   // 认知负荷
            timeDecay.average                    // 时间衰减
        ];

        return new Matrix([features]);
    }

    /**
     * 分析对话内容
     */
    analyzeConversations(conversations) {
        if (!conversations || conversations.length === 0) {
            return { complexity: 0.3, frequency: 0.2, length: 0.1 };
        }

        let totalComplexity = 0;
        let totalLength = 0;
        const timeSpan = this.calculateTimeSpan(conversations);

        conversations.forEach(conv => {
            // 使用natural库分析文本复杂度
            const content = conv.content || '';
            let tokens, sentences;

            try {
                // 兼容不同版本的natural库，并优化中文支持
                if (natural.WordTokenizer && typeof natural.WordTokenizer.tokenize === 'function') {
                    tokens = natural.WordTokenizer.tokenize(content);
                } else if (natural.WordTokenizer) {
                    const tokenizer = new natural.WordTokenizer();
                    tokens = tokenizer.tokenize(content);
                } else {
                    tokens = [];
                }

                // 如果natural分词结果为空（常见于中文），使用改进的备用方案
                if (!tokens || tokens.length === 0) {
                    // 中文和英文混合分词：按空格、标点和中文字符分割
                    tokens = content
                        .replace(/[，。！？；：""''（）【】《》]/g, ' ') // 替换中文标点为空格
                        .split(/\s+/) // 按空格分割
                        .filter(token => token.length > 0)
                        .flatMap(token => {
                            // 如果包含中文，按字符分割
                            if (/[\u4e00-\u9fff]/.test(token)) {
                                return token.split('').filter(char => /[\u4e00-\u9fff\w]/.test(char));
                            }
                            return [token];
                        });
                }

                if (natural.SentenceTokenizer && typeof natural.SentenceTokenizer.tokenize === 'function') {
                    sentences = natural.SentenceTokenizer.tokenize(content);
                } else if (natural.SentenceTokenizer) {
                    const sentenceTokenizer = new natural.SentenceTokenizer();
                    sentences = sentenceTokenizer.tokenize(content);
                } else {
                    sentences = [];
                }

                // 如果句子分割结果为空，使用改进的备用方案
                if (!sentences || sentences.length === 0) {
                    // 中英文句子分割：支持中英文标点
                    sentences = content
                        .split(/[.!?。！？；]+/)
                        .map(sentence => sentence.trim())
                        .filter(sentence => sentence.length > 0);
                }
            } catch (error) {
                this.logger.warning('世界树VCP', `文本分析失败，使用备用方案: ${error.message}`);
                // 备用方案：改进的中文支持
                tokens = content
                    .replace(/[，。！？；：""''（）【】《》]/g, ' ')
                    .split(/\s+/)
                    .filter(token => token.length > 0);
                sentences = content
                    .split(/[.!?。！？；]+/)
                    .map(sentence => sentence.trim())
                    .filter(sentence => sentence.length > 0);
            }

            // 计算复杂度指标
            const avgWordsPerSentence = tokens.length / Math.max(sentences.length, 1);
            const uniqueWords = new Set(tokens).size;
            const lexicalDiversity = uniqueWords / Math.max(tokens.length, 1);

            totalComplexity += (avgWordsPerSentence * 0.3 + lexicalDiversity * 0.7);
            totalLength += (conv.content || '').length;
        });

        return {
            complexity: Math.min(1, totalComplexity / conversations.length / 10),
            frequency: Math.min(1, conversations.length / Math.max(timeSpan, 1)),
            length: Math.min(1, totalLength / conversations.length / 100)
        };
    }

    /**
     * 计算时间跨度（小时）
     */
    calculateTimeSpan(conversations) {
        if (!conversations || conversations.length < 2) return 1;

        const timestamps = conversations.map(c => moment(c.timestamp));
        const earliest = moment.min(timestamps);
        const latest = moment.max(timestamps);

        return Math.max(1, latest.diff(earliest, 'hours'));
    }

    /**
     * 计算时间衰减
     */
    calculateTimeDecay(conversations) {
        if (!conversations || conversations.length === 0) {
            return { average: 0.5, recent: 0.3 };
        }

        const now = moment();
        const decayValues = conversations.map(conv => {
            const timeDiff = now.diff(moment(conv.timestamp), 'hours');
            return Math.exp(-timeDiff / 24); // 24小时半衰期
        });

        return {
            average: decayValues.reduce((a, b) => a + b, 0) / decayValues.length,
            recent: decayValues.slice(0, 3).reduce((a, b) => a + b, 0) / Math.min(3, decayValues.length)
        };
    }

    /**
     * 计算专注水平
     */
    calculateFocusLevel(timePeriod, contextFactors) {
        let baseFocus = 60; // 基础专注水平

        // 时间因素
        const timeMultiplier = this.psychologyAlgorithm.timeFactors[timePeriod].focus;
        baseFocus *= timeMultiplier;

        // 环境干扰因素
        if (contextFactors.distractionLevel) {
            baseFocus *= (1 - contextFactors.distractionLevel * 0.4);
        }

        // 任务复杂度因素
        if (contextFactors.taskComplexity) {
            baseFocus *= (0.8 + contextFactors.taskComplexity * 0.2);
        }

        return this.normalizeValue(baseFocus, 0, 100);
    }

    /**
     * 计算物理属性（支持相互影响的人性化算法）
     */
    computePhysicalAttributes(stateMatrix, realTimeFactors = {}) {
        const features = stateMatrix.getRow(0);

        // 🧠 第一轮：计算基础物理指标（科学关联算法）
        let physicalState = {
            // 只保留核心物理属性，移除心理属性
            energy: this.calculateEnergy(features),
            fatigue: this.calculateFatigue(features),
            hunger: this.calculateHunger(features),
            alertness: this.calculateAlertness(features),
            focus: 0 // 专注度基于其他属性计算
        };

        // 应用实时因子（如果提供）
        if (realTimeFactors.realTimeUpdate) {
            const variation = realTimeFactors.timeVariation || 0;
            const agentSeed = realTimeFactors.agentSpecificSeed || 0;
            const cognitiveLoad = realTimeFactors.cognitiveLoad || 0;

            // 为每个属性添加动态变化
            physicalState.focus += variation * 10 + agentSeed * 20;
            physicalState.energy += variation * 8 + agentSeed * 15;
            physicalState.hunger += variation * 6 + agentSeed * 12;
            physicalState.fatigue += -variation * 7 + agentSeed * 18;
            physicalState.alertness += variation * 12 + agentSeed * 25;

            // 认知负荷影响
            physicalState.focus -= cognitiveLoad * 15;
            physicalState.energy -= cognitiveLoad * 10;
            physicalState.fatigue += cognitiveLoad * 20;
        }

        // 🧠 第二轮：科学的物理属性关联计算
        // 基于生理学原理建立属性间的科学关联

        // 1. 精力-疲劳反向关联（核心关系）
        // 根据生理学，精力和疲劳应该呈反比关系
        const energyFatigueSum = physicalState.energy + physicalState.fatigue;
        if (energyFatigueSum < 80 || energyFatigueSum > 120) {
            // 调整到科学范围内（总和应该在80-120之间）
            const targetSum = 100;
            const ratio = targetSum / energyFatigueSum;
            physicalState.energy = Math.max(15, Math.min(85, physicalState.energy * ratio));
            physicalState.fatigue = Math.max(15, Math.min(85, physicalState.fatigue * ratio));
        }

        // 2. 专注度基于其他属性科学计算
        physicalState.focus = this.calculateFocusFromPhysicalState(physicalState, features);

        // 3. 警觉度与精力正相关，与疲劳负相关
        const alertnessFromEnergy = physicalState.energy * 0.7; // 精力影响70%
        const alertnessFromFatigue = (100 - physicalState.fatigue) * 0.5; // 疲劳影响50%
        physicalState.alertness = Math.max(20, Math.min(95,
            (alertnessFromEnergy + alertnessFromFatigue) / 2
        ));

        // 4. 饥饿感影响精力和疲劳（马斯洛需求层次）
        if (physicalState.hunger > 70) {
            physicalState.energy = Math.max(15, physicalState.energy * 0.8); // 饥饿时精力下降
            physicalState.fatigue = Math.min(85, physicalState.fatigue * 1.2); // 饥饿增加疲劳感
        } else if (physicalState.hunger < 30) {
            physicalState.energy = Math.min(85, physicalState.energy * 1.1); // 饱腹时精力略增
        }

        return physicalState;
    }

    /**
     * 基于物理状态科学计算专注度
     */
    calculateFocusFromPhysicalState(physicalState, features) {
        const [complexity, frequency, length, circadian, timeOfDay, cognitiveLoad] = features;

        // 基础专注度（基于认知负荷和生理节律）
        const stimulation = (complexity + frequency) * 0.5;
        const optimalStimulation = 0.6;
        const stimulationEffect = 1 - Math.abs(stimulation - optimalStimulation);
        const circadianEffect = Math.sin(circadian * Math.PI * 2) * 0.2 + 0.8;
        const cognitiveEffect = Math.max(0.2, 1 - (cognitiveLoad || 0) * 0.8);

        let baseFocus = (stimulationEffect * 0.4 + circadianEffect * 0.3 + cognitiveEffect * 0.3) * 100;

        // 🧠 科学关联：基于其他物理属性调整专注度

        // 1. 精力对专注度的影响（最重要，权重60%）
        const energyEffect = physicalState.energy / 100; // 0-1
        baseFocus *= (0.4 + energyEffect * 0.6); // 精力低时专注度大幅下降

        // 2. 疲劳对专注度的负面影响（权重40%）
        const fatigueEffect = (100 - physicalState.fatigue) / 100; // 0-1
        baseFocus *= (0.6 + fatigueEffect * 0.4); // 疲劳高时专注度下降

        // 3. 饥饿感的影响（马斯洛需求层次，权重30%）
        if (physicalState.hunger > 70) {
            baseFocus *= 0.7; // 饥饿时专注度大幅下降
        } else if (physicalState.hunger > 50) {
            baseFocus *= 0.85; // 有点饿时轻微影响
        } else if (physicalState.hunger < 30) {
            baseFocus *= 1.1; // 饱腹时专注度略微提升
        }

        // 4. 警觉度的影响（权重20%）
        const alertnessEffect = physicalState.alertness / 100;
        baseFocus *= (0.8 + alertnessEffect * 0.2);

        return Math.max(10, Math.min(95, baseFocus));
    }

    /**
     * 计算专注度（基于认知负荷理论 + 生理需求影响）
     */
    calculateFocus(features, physicalState = {}) {
        const [complexity, frequency, length, circadian, timeOfDay, cognitiveLoad, timeDecay] = features;

        // Yerkes-Dodson定律：适度的刺激提高专注度
        const stimulation = (complexity + frequency) * 0.5;
        const optimalStimulation = 0.6;
        const stimulationEffect = 1 - Math.abs(stimulation - optimalStimulation);

        // 生理节律影响
        const circadianEffect = Math.sin(circadian * Math.PI * 2) * 0.2 + 0.8;

        // 认知负荷影响
        const cognitiveEffect = Math.max(0.2, 1 - cognitiveLoad * 0.8);

        // 基础专注度
        let baseFocus = (stimulationEffect * 0.4 + circadianEffect * 0.3 + cognitiveEffect * 0.3) * 100;

        // 🧠 人性化调整：生理需求对专注度的影响
        let focusModifiers = 1.0;

        // 饥饿感严重影响专注度（马斯洛需求层次）
        const hunger = physicalState.hunger || 0;
        if (hunger > 70) {
            focusModifiers *= 0.6; // 非常饿时专注度大幅下降
        } else if (hunger > 40) {
            focusModifiers *= 0.8; // 有点饿时轻微影响
        }

        // 疲劳直接影响专注度
        const fatigue = physicalState.fatigue || 0;
        if (fatigue > 80) {
            focusModifiers *= 0.4; // 极度疲劳时专注度严重下降
        } else if (fatigue > 60) {
            focusModifiers *= 0.7; // 疲劳时专注度下降
        }

        // 压力的双重影响（适度压力提升，过度压力降低）
        const stress = physicalState.stress || 0;
        if (stress > 70) {
            focusModifiers *= 0.5; // 高压力严重影响专注度
        } else if (stress > 40) {
            focusModifiers *= 0.8; // 中等压力轻微影响
        } else if (stress > 20) {
            focusModifiers *= 1.1; // 适度压力提升专注度
        }

        // 心情影响专注度
        const mood = physicalState.mood || 50;
        if (mood < 30) {
            focusModifiers *= 0.7; // 心情差时专注度下降
        } else if (mood > 70) {
            focusModifiers *= 1.1; // 心情好时专注度提升
        }

        const focus = baseFocus * focusModifiers;
        return Math.max(5, Math.min(100, focus));
    }

    /**
     * 计算精力水平（基于能量消耗模型 + 生理状态影响）
     */
    calculateEnergy(features, physicalState = {}) {
        const [complexity, frequency, length, circadian, timeOfDay, cognitiveLoad, timeDecay] = features;

        // 🔋 修复算法：确保精力值在合理范围内

        // 1. 基础精力（基于时间，范围50-80）
        const hour = new Date().getHours();
        let baseEnergy = 65; // 默认基础精力

        // 根据时间调整基础精力
        if (hour >= 6 && hour <= 9) baseEnergy = 80;   // 早晨精力充沛
        else if (hour >= 10 && hour <= 12) baseEnergy = 75; // 上午良好
        else if (hour >= 13 && hour <= 15) baseEnergy = 60; // 午后低谷
        else if (hour >= 16 && hour <= 18) baseEnergy = 70; // 下午回升
        else if (hour >= 19 && hour <= 21) baseEnergy = 55; // 傍晚下降
        else baseEnergy = 40; // 夜间低谷

        // 2. 生理节律微调（±10）
        const circadianEffect = Math.sin(circadian * Math.PI * 2) * 10;

        // 3. 活动消耗（限制在合理范围）
        const activityCost = Math.min(25, Math.max(0, (complexity * 5 + frequency * 3 + length * 2)));

        // 4. 恢复因子（基于时间衰减）
        const recoveryFactor = Math.min(15, Math.max(0, timeDecay * 20));

        // 5. 计算基础精力（确保不会太低）
        let energyLevel = Math.max(30, baseEnergy + circadianEffect - activityCost + recoveryFactor);

        // 🔋 人性化调整：生理状态对精力的影响（使用加法而非乘法避免极值）
        let energyAdjustment = 0;

        // 饥饿影响精力
        const hunger = physicalState.hunger || 0;
        if (hunger > 80) {
            energyAdjustment -= 20; // 极度饥饿时精力大幅下降
        } else if (hunger > 60) {
            energyAdjustment -= 12; // 很饿时精力下降
        } else if (hunger > 40) {
            energyAdjustment -= 5; // 有点饿时轻微影响
        } else if (hunger < 20) {
            energyAdjustment += 5; // 饱腹时精力略微提升
        }

        // 疲劳影响精力
        const fatigue = physicalState.fatigue || 0;
        if (fatigue > 80) {
            energyAdjustment -= 25; // 极度疲劳时精力严重下降
        } else if (fatigue > 60) {
            energyAdjustment -= 15; // 疲劳时精力下降
        } else if (fatigue > 40) {
            energyAdjustment -= 8; // 轻微疲劳时精力下降
        } else if (fatigue < 20) {
            energyAdjustment += 8; // 精神饱满时精力提升
        }

        // 压力影响精力
        const stress = physicalState.stress || 0;
        if (stress > 70) {
            energyAdjustment -= 15; // 高压力消耗精力
        } else if (stress > 40) {
            energyAdjustment -= 8; // 中等压力消耗精力
        } else if (stress < 20) {
            energyAdjustment += 5; // 低压力时精力稳定
        }

        // 心情影响精力
        const mood = physicalState.mood || 50;
        if (mood < 20) {
            energyAdjustment -= 15; // 极度低落时精力不足
        } else if (mood < 40) {
            energyAdjustment -= 8; // 心情差时精力下降
        } else if (mood > 80) {
            energyAdjustment += 15; // 心情极好时精力充沛
        } else if (mood > 60) {
            energyAdjustment += 8; // 心情好时精力提升
        }

        // 应用调整
        energyLevel += energyAdjustment;

        // 确保在合理范围内（20-95）
        return Math.max(20, Math.min(95, energyLevel));
    }

    /**
     * 计算饥饿感（基于时间、活动强度和生理状态）
     */
    calculateHunger(features, physicalState = {}) {
        const [complexity, frequency, length, circadian, timeOfDay] = features;

        // 基于时间的饥饿感（更真实的饥饿周期）
        const hoursSinceLastMeal = (timeOfDay % 6); // 假设6小时为一个饥饿周期
        let timeBasedHunger = Math.pow(hoursSinceLastMeal / 6, 1.5) * 60; // 非线性增长

        // 活动强度影响代谢（消耗更多能量）
        const metabolicRate = (complexity * 8 + frequency * 6 + length * 4);

        // 🍽️ 人性化调整：生理状态对饥饿感的影响
        let hungerModifiers = 1.0;

        // 精力不足时更容易感到饥饿（身体需要补充能量）
        const energy = physicalState.energy || 50;
        if (energy < 30) {
            hungerModifiers *= 1.4; // 精力不足时饥饿感增强
        } else if (energy < 50) {
            hungerModifiers *= 1.2; // 精力一般时轻微增强
        }

        // 压力影响食欲（有些人压力大时更饿，有些人没食欲）
        const stress = physicalState.stress || 0;
        if (stress > 70) {
            hungerModifiers *= 1.3; // 高压力时可能暴饮暴食
        } else if (stress > 40) {
            hungerModifiers *= 0.9; // 中等压力时食欲略减
        }

        // 疲劳时身体需要更多能量
        const fatigue = physicalState.fatigue || 0;
        if (fatigue > 70) {
            hungerModifiers *= 1.2; // 疲劳时身体需要更多营养
        }

        // 生理节律影响食欲（早晚食欲不同）
        const circadianAppetite = (Math.sin(circadian * Math.PI * 2 + Math.PI) + 1) * 0.2 + 0.8;
        hungerModifiers *= circadianAppetite;

        const hunger = (timeBasedHunger + metabolicRate) * hungerModifiers;
        return Math.max(0, Math.min(100, hunger));
    }

    /**
     * 计算疲劳度（基于累积负荷）
     */
    calculateFatigue(features) {
        const [complexity, frequency, length, circadian, timeOfDay, cognitiveLoad] = features;

        // 累积疲劳
        const cumulativeFatigue = (complexity * 8 + frequency * 12 + cognitiveLoad * 15);

        // 生理节律的疲劳影响
        const circadianFatigue = (1 - Math.sin(circadian * Math.PI * 2)) * 30;

        const fatigue = cumulativeFatigue + circadianFatigue;
        return Math.max(0, Math.min(100, fatigue));
    }

    /**
     * 计算警觉性（基于注意力资源理论）
     */
    calculateAlertness(features) {
        const [complexity, frequency, length, circadian, timeOfDay, cognitiveLoad, timeDecay] = features;

        // 基础警觉性（生理节律）
        const baseAlertness = (Math.sin(circadian * Math.PI * 2) + 1) * 40 + 20;

        // 刺激维持警觉性
        const stimulationEffect = Math.min(30, (frequency + complexity) * 20);

        // 疲劳降低警觉性
        const fatigueEffect = -cognitiveLoad * 25;

        // 时间衰减影响
        const timeEffect = timeDecay * 15;

        const alertness = baseAlertness + stimulationEffect + fatigueEffect + timeEffect;
        return Math.max(10, Math.min(100, alertness));
    }

    /**
     * 获取时间因子（科学的生理节律计算）
     */
    getTimeFactors() {
        const now = moment();
        const hour = now.hour();
        const minute = now.minute();

        // 计算一天中的时间比例 (0-1)
        const timeOfDay = (hour + minute / 60) / 24;

        // 计算生理节律（基于人体生物钟）
        // 使用双峰模型：上午10点和下午3点为峰值
        const morningPeak = Math.exp(-Math.pow((hour - 10) / 3, 2));
        const afternoonPeak = Math.exp(-Math.pow((hour - 15) / 3, 2));
        const circadianRhythm = Math.max(morningPeak, afternoonPeak);

        return {
            timeOfDay,
            circadianRhythm,
            hour,
            minute,
            energyModifier: this.calculateEnergyModifier(hour),
            focusModifier: this.calculateFocusModifier(hour),
            stressModifier: this.calculateStressModifier(hour)
        };
    }

    /**
     * 计算能量修正因子
     */
    calculateEnergyModifier(hour) {
        // 基于人体皮质醇分泌规律
        if (hour >= 6 && hour <= 9) return 1.2;   // 早晨皮质醇高峰
        if (hour >= 10 && hour <= 12) return 1.0; // 上午稳定期
        if (hour >= 13 && hour <= 15) return 0.8; // 午后低谷
        if (hour >= 16 && hour <= 18) return 1.1; // 下午回升
        if (hour >= 19 && hour <= 21) return 0.9; // 傍晚下降
        return 0.6; // 夜间低谷
    }

    /**
     * 计算专注修正因子
     */
    calculateFocusModifier(hour) {
        // 基于认知能力的昼夜节律
        if (hour >= 8 && hour <= 11) return 1.3;  // 上午专注高峰
        if (hour >= 14 && hour <= 16) return 1.1; // 下午次高峰
        if (hour >= 17 && hour <= 19) return 0.9; // 傍晚下降
        if (hour >= 20 && hour <= 22) return 0.7; // 晚间低谷
        return 0.5; // 深夜最低
    }

    /**
     * 计算压力修正因子
     */
    calculateStressModifier(hour) {
        // 基于压力激素分泌模式
        if (hour >= 7 && hour <= 9) return 1.2;   // 早晨压力高峰
        if (hour >= 10 && hour <= 16) return 0.8; // 白天相对稳定
        if (hour >= 17 && hour <= 19) return 1.1; // 下班时间压力
        return 0.6; // 其他时间较低
    }

    /**
     * 数值标准化
     */
    normalizeValue(value, min, max) {
        return Math.max(min, Math.min(max, value));
    }

    /**
     * 获取默认心理状态
     */
    getDefaultPsychologyState() {
        return {
            stress: 0,
            emotion: 0,
            energy: 70,
            mood: 0,
            focus: 60,
            overallScore: 65,
            timePeriod: this.getTimePeriod(),
            contextFactors: {},
            timestamp: this.getCurrentLocalTime()
        };
    }

    /**
     * 使用本地算法生成心理活动内容
     */
    async generateLocalPsychologyContent(psychologyState, worldTreeConfig, contextFactors) {
        try {
            const { focus, energy, hunger, fatigue, alertness, stress, mood, timePeriod } = psychologyState;

            // 🧠 更加人性化和真实的心理活动模板
            const templates = {
                // 饥饿相关的心理状态
                very_hungry: [
                    "肚子有点饿了，感觉注意力有些分散，总是想着吃的。",
                    "饥饿感让我有点难以集中精神，脑子里老是想着食物。",
                    "现在很想吃点什么，这种饥饿感影响了我的思考。",
                    "肚子在提醒我该吃饭了，饥饿让我有些烦躁。"
                ],
                hungry: [
                    "有点饿了，不过还能坚持，只是偶尔会想到食物。",
                    "轻微的饥饿感，让我意识到身体需要补充能量了。",
                    "感觉有些饿，但还不至于影响我的专注度。"
                ],

                // 疲劳相关的心理状态
                exhausted: [
                    "真的很累了，感觉大脑都有些迟钝，需要好好休息。",
                    "疲惫感让我的反应变慢了，思考也变得吃力。",
                    "极度疲劳，感觉连说话都需要额外的努力。",
                    "累到不行，整个人都感觉沉重，很难集中注意力。"
                ],
                tired: [
                    "有些疲倦，但还能继续，只是需要更多的耐心。",
                    "感觉有点累，思考速度可能会慢一些。",
                    "轻微的疲劳感，让我想要放慢节奏。"
                ],

                // 压力相关的心理状态
                high_stress: [
                    "压力很大，心跳有些快，但这也让我更加警觉和专注。",
                    "感受到强烈的压力，肌肉都有些紧张，需要深呼吸调节。",
                    "高压状态下，思维变得更加敏锐，但也容易焦虑。",
                    "压力山大，感觉神经紧绷，但也激发了我的潜能。"
                ],
                moderate_stress: [
                    "有一定压力，但这种适度的紧张感让我保持警觉。",
                    "感受到一些压力，但还在可控范围内，反而有助于专注。",
                    "轻微的压力感，让我更加认真对待当前的情况。"
                ],

                // 综合状态模板
                optimal_state: [
                    "现在的状态很好，精力充沛，思路清晰，感觉什么都能应对。",
                    "身心状态都很棒，专注力很强，对接下来的事情充满期待。",
                    "感觉自己处在最佳状态，能量满满，思维敏捷。"
                ],
                struggling_state: [
                    "现在的状态不太理想，多种因素影响了我的表现，需要调整。",
                    "身体和精神都有些疲惫，但我会尽力保持专业和认真。",
                    "虽然状态不佳，但我会努力克服这些困难。"
                ],

                // 情绪状态
                positive_mood: [
                    "心情很好，对一切都充满兴趣，很享受当下的感觉。",
                    "今天心境很积极，愿意分享想法，也很乐意倾听别人。",
                    "感觉很愉快，这种好心情让我更有耐心和理解力。"
                ],
                negative_mood: [
                    "心情有些低落，可能会更倾向于深度思考而不是闲聊。",
                    "情绪不太高，但这不会影响我对事情的认真态度。",
                    "心情有点沉重，需要更多时间来组织语言和思考。"
                ]
            };

            let content = "";

            // 🍽️ 优先处理生理需求（马斯洛需求层次理论）
            if (hunger > 80) {
                content += this.getRandomTemplate(templates.very_hungry) + " ";
            } else if (hunger > 50) {
                content += this.getRandomTemplate(templates.hungry) + " ";
            }

            // 😴 疲劳状态影响
            if (fatigue > 80) {
                content += this.getRandomTemplate(templates.exhausted) + " ";
            } else if (fatigue > 60) {
                content += this.getRandomTemplate(templates.tired) + " ";
            }

            // 💪 压力状态影响
            if (stress > 70) {
                content += this.getRandomTemplate(templates.high_stress) + " ";
            } else if (stress > 40) {
                content += this.getRandomTemplate(templates.moderate_stress) + " ";
            }

            // 🧠 综合状态评估
            const overallScore = (focus + energy + alertness - fatigue - stress + mood) / 6;
            if (overallScore > 70) {
                content += this.getRandomTemplate(templates.optimal_state) + " ";
            } else if (overallScore < 40) {
                content += this.getRandomTemplate(templates.struggling_state) + " ";
            }

            // 😊 情绪状态
            if (mood > 70) {
                content += this.getRandomTemplate(templates.positive_mood);
            } else if (mood < 40) {
                content += this.getRandomTemplate(templates.negative_mood);
            }

            // 添加时间相关的心理活动
            content += this.generateTimePeriodContent(timePeriod);

            return content.trim();

        } catch (error) {
            this.logger.error('世界树VCP', '本地心理内容生成失败:', error.message);
            return "正在思考中...";
        }
    }

    /**
     * 获取随机模板
     */
    getRandomTemplate(templates) {
        return templates[Math.floor(Math.random() * templates.length)];
    }

    /**
     * 生成时间段相关内容
     */
    generateTimePeriodContent(timePeriod) {
        const timeContent = {
            morning: [
                " 早晨的感觉真好，头脑清醒，整个人都很有精神。",
                " 清晨的空气很清新，让我感觉思路特别清晰。",
                " 新的一天开始了，感觉充满了可能性。",
                " 早上的时光总是让我感到平静和专注。"
            ],
            afternoon: [
                " 下午的阳光很温暖，让人感觉很舒服，适合好好聊聊。",
                " 午后时光，不急不躁，正好可以深入地交流一些想法。",
                " 下午的节奏刚好，既不会太匆忙，也不会太慵懒。",
                " 这个时候的我状态正好，可以专心地处理各种事情。"
            ],
            evening: [
                " 傍晚了，一天下来有了不少感悟和体会。",
                " 夕阳西下的时候，总是让我想要回顾和思考。",
                " 傍晚时分，心情会变得更加沉静和深刻。",
                " 这个时候的我会更加感性一些，容易被触动。"
            ],
            night: [
                " 夜深了，周围很安静，正是深度思考的好时候。",
                " 夜晚的宁静让我能够更专注地思考问题。",
                " 深夜时分，思绪会变得更加深邃和敏感。",
                " 夜晚的我会更加内省，喜欢探讨一些深层的话题。"
            ]
        };

        const options = timeContent[timePeriod];
        return options ? options[Math.floor(Math.random() * options.length)] : "";
    }

    /**
     * 使用API生成智能心理活动内容（优化的messages数组结构）
     */
    async generateIntelligentPsychologyContent(userId, agentName, psychologyState, worldTreeConfig, fullContext) {
        try {
            // 构建优化的messages数组
            const messages = await this.buildOptimizedMessages(
                agentName,
                psychologyState,
                worldTreeConfig,
                fullContext
            );

            // 调用API进行心理分析
            const response = await this.callPsychologyAPIWithMessages(messages);

            // 保存生成的心理独白到专门的表
            if (response) {
                await this.saveMonologueToWorldTree(userId, agentName, response, psychologyState, fullContext);
            }

            return response || this.generateFallbackPsychologyContent(psychologyState);

        } catch (error) {
            this.logger.warning('世界树VCP', `API心理分析失败，使用备用方案:`, error.message);
            return this.generateFallbackPsychologyContent(psychologyState);
        }
    }

    /**
     * 获取数据库中最新的心理活动
     */
    async getLatestPsychologyActivity(userId, agentName) {
        try {
            const latestActivity = await this.dbGet(`
                SELECT monologue_content, psychology_state, context_data, created_time, generation_method
                FROM worldtree_psychology_monologues
                WHERE user_id = ? AND agent_name = ?
                ORDER BY created_time DESC
                LIMIT 1
            `, [userId, agentName]);

            if (latestActivity) {
                return {
                    content: latestActivity.monologue_content,
                    psychologyState: JSON.parse(latestActivity.psychology_state || '{}'),
                    contextData: JSON.parse(latestActivity.context_data || '{}'),
                    timestamp: latestActivity.created_time,
                    generationMethod: latestActivity.generation_method
                };
            }

            return null;
        } catch (error) {
            this.logger.error('世界树VCP', '获取最新心理活动失败:', error.message);
            return null;
        }
    }



    /**
     * 异步生成智能心理活动内容（不阻塞主程序）
     */
    async generateIntelligentPsychologyContentAsync(userId, agentName, psychologyState, worldTreeConfig, fullContext, lastMonologue = null) {
        try {
            const asyncCallId = Math.random().toString(36).substr(2, 9);
            this.logger.info('世界树VCP', `🚀 开始异步生成心理独白 [${asyncCallId}]: ${agentName}`);

            // 🔧 获取对话上下文（如果是主程序触发）
            let conversationContext = null;
            const isConversationTriggered = fullContext.updateType === 'conversation_triggered' || fullContext.hasRecentConversation;

            if (isConversationTriggered) {
                this.logger.debug('世界树VCP', `主程序触发，获取对话上下文: ${agentName}`);
                conversationContext = await this.getRecentConversations(userId, agentName);
            }

            // 构建优化的messages数组，包含上一次独白的参考和对话上下文
            const messages = await this.buildOptimizedMessages(
                agentName,
                psychologyState,
                worldTreeConfig,
                fullContext,
                lastMonologue, // 传入上一次独白作为参考
                conversationContext // 传入对话上下文
            );

            // 调用API进行心理分析
            const response = await this.callPsychologyAPIWithMessages(messages);

            if (response) {
                // 保存生成的心理独白到专门的表
                this.logger.info('世界树VCP', `💾 开始保存心理独白 [${asyncCallId}]: ${agentName}`);
                await this.saveMonologueToWorldTree(userId, agentName, response, psychologyState, fullContext);
                this.logger.info('世界树VCP', `✅ 异步心理独白生成并保存完成 [${asyncCallId}]: ${agentName}`);
                return response;
            } else {
                this.logger.warning('世界树VCP', `⚠️ 异步心理独白生成无响应 [${asyncCallId}]: ${agentName}`);
                return null;
            }

        } catch (error) {
            this.logger.error('世界树VCP', `异步心理独白生成失败 [${agentName}]:`, error.message);
            // 异步操作失败不影响主程序，只记录日志
            return null;
        }
    }

    /**
     * 构建优化的Messages数组（使用Agent文件+结构化上下文+上一次独白参考+对话上下文）
     */
    async buildOptimizedMessages(agentName, psychologyState, worldTreeConfig, fullContext, lastMonologue = null, conversationContext = null) {
        const messages = [];

        // 1. System消息 - 直接读取Agent文件内容
        try {
            const agentFilePath = path.join(__dirname, '../../Agent', `${agentName}.txt`);
            const systemContent = await fs.readFile(agentFilePath, 'utf-8');
            messages.push({
                role: 'system',
                content: systemContent.trim()
            });
        } catch (error) {
            this.logger.warning('世界树VCP', `未找到Agent文件: ${agentName}.txt，使用默认身份`);
            messages.push({
                role: 'system',
                content: `你是${agentName}，一位AI助手。请根据当前状态生成真实的内心独白。`
            });
        }

        // 2. User消息 - 结构化的状态和上下文信息（包含对话记录）
        const userContent = await this.buildStructuredUserContent(agentName, psychologyState, worldTreeConfig, fullContext, lastMonologue, conversationContext);
        messages.push({
            role: 'user',
            content: userContent
        });

        return messages;
    }

    /**
     * 构建结构化的User消息内容（包含上一次独白参考和对话上下文）
     */
    async buildStructuredUserContent(agentName, psychologyState, worldTreeConfig, fullContext, lastMonologue = null, conversationContext = null) {
        let content = '';
        const timePeriod = psychologyState.timePeriod || this.getTimePeriod();
        const timePeriodName = this.getTimePeriodName(timePeriod);

        // === 我现在的状态 ===
        content += '=== 我现在的状态 ===\n';
        content += `现在是 ${fullContext.timestamp}，${timePeriodName}时段。\n\n`;

        // 身体感受（更人性化的描述）
        content += '身体感受:\n';
        const energyLevel = psychologyState.energy || 0;
        const fatigueLevel = psychologyState.fatigue || 0;
        const focusLevel = psychologyState.focus || 0;
        const alertnessLevel = psychologyState.alertness || 0;
        const hungerLevel = psychologyState.hunger || 0;

        if (energyLevel > 70) {
            content += `• 精力充沛，感觉状态很好\n`;
        } else if (energyLevel > 40) {
            content += `• 精力还算可以，不算太累\n`;
        } else {
            content += `• 有些疲惫，精力不太够\n`;
        }

        if (fatigueLevel > 60) {
            content += `• 明显感到疲劳，想要休息\n`;
        } else if (fatigueLevel > 30) {
            content += `• 有轻微的疲劳感\n`;
        } else {
            content += `• 身体状态还不错，不太累\n`;
        }

        if (focusLevel > 70) {
            content += `• 注意力很集中，思维清晰\n`;
        } else if (focusLevel > 40) {
            content += `• 注意力还算集中\n`;
        } else {
            content += `• 有点难以集中注意力\n`;
        }

        if (alertnessLevel > 70) {
            content += `• 很有警觉性，反应敏锐\n`;
        } else if (alertnessLevel > 40) {
            content += `• 警觉性一般\n`;
        } else {
            content += `• 感觉有点迟钝，反应不够快\n`;
        }

        if (hungerLevel > 60) {
            content += `• 有点饿了，肚子在提醒我该吃东西了\n`;
        } else if (hungerLevel < 30) {
            content += `• 不饿，刚吃过或者还很饱\n`;
        }

        // === 当前情境 ===
        if (worldTreeConfig) {
            content += '=== 当前情境 ===\n';
            if (worldTreeConfig.worldBackground) {
                content += `环境: ${worldTreeConfig.worldBackground}\n`;
            }
            if (worldTreeConfig.characterSchedules) {
                const scheduleText = this.extractCurrentSchedule(worldTreeConfig.characterSchedules);
                if (scheduleText) {
                    content += `日程: ${scheduleText}\n`;
                }
            }
            content += '\n';
        }

        // === 最近对话记录 ===（如果有对话记录）
        if (conversationContext && conversationContext.length > 0) {
            content += '=== 最近对话记录 ===\n';
            content += `共 ${conversationContext.length} 条记录，按时间倒序排列（最新在前）:\n\n`;

            conversationContext.forEach((conv, index) => {
                // 使用已经格式化好的时间
                const timeStr = conv.formattedTime || new Date(conv.timestamp).toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    timeZone: 'Asia/Shanghai'
                });

                content += `${index + 1}. [${timeStr}]\n`;

                // 显示用户消息（包含用户名称）
                if (conv.userMessage && conv.userMessage.trim()) {
                    content += `   ${conv.userId || '用户'}: "${conv.userMessage.substring(0, 100)}${conv.userMessage.length > 100 ? '...' : ''}"\n`;
                }

                // 显示AI回复（不包含名称，更自然）
                if (conv.aiResponse && conv.aiResponse.trim()) {
                    content += `   我: "${conv.aiResponse.substring(0, 100)}${conv.aiResponse.length > 100 ? '...' : ''}"\n`;
                }

                content += '\n';
            });
            content += '注意: 基于最近的对话内容和时间顺序，生成符合当前情境的内心独白。\n\n';
        }

        // === 上一次独白 ===
        if (lastMonologue && lastMonologue.content) {
            content += '=== 上一次独白 ===\n';
            content += `"${lastMonologue.content.substring(0, 100)}..."\n`;
            content += '注意: 体现思维连续性，但不要重复。\n\n';
        }

        // === 情感线索 ===（如果有对话记录）
        if (conversationContext && conversationContext.length > 0) {
            content += '=== 情感线索分析 ===\n';
            content += '基于最近的对话记录，分析可能的情感变化和内心感受：\n';

            // 分析对话中的情感线索
            const recentConv = conversationContext[0]; // 最新的对话
            if (recentConv) {
                if (recentConv.userMessage && recentConv.userMessage.includes('你好')) {
                    content += '• 感受到友好的问候，可能带来温暖感\n';
                } else if (recentConv.userMessage && (recentConv.userMessage.includes('怎么样') || recentConv.userMessage.includes('近况'))) {
                    content += '• 感受到关心和关注，可能产生被重视的感觉\n';
                } else if (recentConv.userMessage && (recentConv.userMessage.includes('天气') || recentConv.userMessage.includes('下雨'))) {
                    content += '• 天气话题的交流，可能带来关心和温暖的感觉\n';
                } else if (recentConv.userMessage && recentConv.userMessage.includes('对')) {
                    content += '• 简短的确认回应，可能表示认同或关注\n';
                } else {
                    content += '• 正常的交流互动，保持平和的心境\n';
                }
            }
            content += '\n';
        }

        // === 思维连续性 ===
        if (lastMonologue && lastMonologue.content) {
            content += '=== 思维连续性 ===\n';
            content += `上一次的思考重点: "${lastMonologue.content.substring(0, 50)}..."\n`;
            content += '注意: 在此基础上自然延续思维，避免重复相同的想法。\n\n';
        }

        // === 内心独白生成 ===
        content += '\n=== 内心独白生成 ===\n';
        content += '请基于以上信息，生成一段真实的内心独白：\n\n';
        content += '要求：\n';
        content += '• 120-200字左右，自然流畅\n';
        content += '• 以第一人称"我"的视角，体现真实的内心声音\n';
        content += '• 结合当前的身体感受和心理状态\n';
        if (conversationContext && conversationContext.length > 0) {
            content += '• 可以回想刚才的对话，表达内心的真实感受\n';
        }
        if (lastMonologue) {
            content += '• 在之前的思考基础上自然延续，但不要重复相同的想法\n';
        }
        content += '• 语言要像真实的内心想法，不要太正式\n';
        content += '• 不使用emoji、颜文字或特殊符号\n\n';
        content += '请开始生成内心独白：';

        return content;
    }

    /**
     * 构建心理分析提示词
     */
    buildPsychologyAnalysisPrompt(agentName, physicalState, worldTreeConfig, recentConversations, contextFactors) {
        const currentTime = this.getCurrentLocalTime();
        const timePeriod = this.getTimePeriodName(this.getTimePeriod());

        let prompt = `你是${agentName}，请以第一人称的角度，根据当前状态和情况，写出你此刻的内心想法。

【当前时间】${currentTime} (${timePeriod}时段)

【你的物理状态】
- 专注程度: ${physicalState.focus.toFixed(1)}/100
- 精力水平: ${physicalState.energy.toFixed(1)}/100
- 饥饿感: ${physicalState.hunger.toFixed(1)}/100
- 疲劳度: ${physicalState.fatigue.toFixed(1)}/100
- 警觉性: ${physicalState.alertness.toFixed(1)}/100

`;

        // 添加世界背景
        if (worldTreeConfig?.worldBackground) {
            prompt += `【你的身份背景】\n${worldTreeConfig.worldBackground}\n\n`;
        }

        // 添加时间段设定
        if (worldTreeConfig?.timeArchitecture) {
            const currentPeriod = this.getTimePeriod();
            if (worldTreeConfig.timeArchitecture[currentPeriod]) {
                prompt += `【${timePeriod}时段的你】\n${worldTreeConfig.timeArchitecture[currentPeriod]}\n\n`;
            }
        }

        // 添加最近对话
        if (recentConversations && recentConversations.length > 0) {
            prompt += `【最近的对话内容】\n`;
            recentConversations.slice(0, 3).forEach((conv, index) => {
                prompt += `${index + 1}. ${conv.content}\n`;
            });
            prompt += `\n`;
        }

        // 添加上下文因子
        if (contextFactors.hasRecentConversation) {
            prompt += `【当前情况】刚刚有人和你对话\n\n`;
        }

        prompt += `请以第一人称写出你此刻的内心想法，要求：
1. 体现当前的物理状态（专注度、精力、疲劳等）
2. 符合你的身份背景和性格
3. 考虑当前时间段的特点
4. 自然真实，就像真正的内心独白
5. 控制在50-80字之间
6. 不要使用引号或其他格式符号

请直接输出内心想法：`;

        return prompt;
    }

    /**
     * 使用优化的Messages数组调用心理分析API
     */
    async callPsychologyAPIWithMessages(messages) {
        try {
            this.logger.info('世界树VCP', `调用API生成心理独白，messages数量: ${messages.length}`);

            // 调试日志：显示消息结构概览
            const messageStructure = messages.map((msg, index) => {
                const preview = msg.content.substring(0, 80).replace(/\n/g, ' ');
                return `[${index}] ${msg.role}: ${preview}...`;
            });
            this.logger.debug('世界树VCP', `消息结构: ${messageStructure.join(' | ')}`);

            // 调试日志：显示消息结构概览
            if (this.config.debugMode) {
                messages.forEach((msg, index) => {
                    const preview = msg.content.substring(0, 100).replace(/\n/g, ' ');
                    this.logger.debug('世界树VCP', `Message[${index}] ${msg.role}: ${preview}...`);
                });
            }

            const response = await axios.post(
                `${this.config.apiUrl}/v1/chat/completions`,
                {
                    model: this.config.model,
                    messages: messages,
                },
                {
                    headers: {
                        'Authorization': `Bearer ${this.config.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    timeout: this.config.timeout
                }
            );

            if (response.data?.choices?.[0]?.message?.content) {
                const content = response.data.choices[0].message.content.trim();
                this.logger.info('世界树VCP', `API生成心理独白成功，长度: ${content.length}字符`);
                return content;
            }

            throw new Error('API响应格式错误');

        } catch (error) {
            this.logger.error('世界树VCP', 'API调用失败:', error.message);
            throw error;
        }
    }

    /**
     * 调用心理分析API（兼容旧版本）
     */
    async callPsychologyAPI(prompt) {
        return await this.callPsychologyAPIWithMessages([
            {
                role: 'user',
                content: prompt
            }
        ]);
    }

    /**
     * 保存心理独白到世界树专用表
     */
    async saveMonologueToWorldTree(userId, agentName, monologueContent, psychologyState, fullContext, generationMethod = 'api') {
        try {
            const currentTime = this.getCurrentLocalTime();

            // 构建上下文数据
            const contextData = {
                timestamp: fullContext?.timestamp || currentTime,
                timePeriod: fullContext?.timePeriod || this.getTimePeriod(),
                emotionalState: fullContext?.emotionalState || null,
                recentConversationsCount: fullContext?.recentConversations?.length || 0
            };

            await this.dbRun(`
                INSERT INTO worldtree_psychology_monologues
                (user_id, agent_name, monologue_content, psychology_state, context_data, generation_method, created_time)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            `, [
                userId,
                agentName,
                monologueContent,
                JSON.stringify(psychologyState),
                JSON.stringify(contextData),
                generationMethod,
                currentTime
            ]);

            this.logger.info('世界树VCP', `心理独白已保存到世界树表: ${agentName} (${generationMethod})`);
        } catch (error) {
            this.logger.error('世界树VCP', '保存心理独白失败:', error.message);
        }
    }

    /**
     * 从世界树表获取最近的心理独白
     */
    async getRecentMonologueFromWorldTree(userId, agentName, limit = 1) {
        try {
            const monologues = await this.dbAll(`
                SELECT monologue_content, psychology_state, context_data, created_time
                FROM worldtree_psychology_monologues
                WHERE user_id = ? AND agent_name = ?
                ORDER BY created_time DESC
                LIMIT ?
            `, [userId, agentName, limit]);

            return monologues.map(m => ({
                content: m.monologue_content,
                psychologyState: JSON.parse(m.psychology_state || '{}'),
                contextData: JSON.parse(m.context_data || '{}'),
                createdTime: m.created_time
            }));
        } catch (error) {
            this.logger.warning('世界树VCP', '获取历史心理独白失败:', error.message);
            return [];
        }
    }

    /**
     * 提取当前时段的日程安排
     */
    extractCurrentSchedule(characterSchedules) {
        if (!characterSchedules) return '';

        try {
            // 处理新格式
            if (characterSchedules.schedules && Array.isArray(characterSchedules.schedules)) {
                const currentHour = new Date().getHours();
                for (const schedule of characterSchedules.schedules) {
                    if (schedule.time && schedule.activity) {
                        const timeRange = schedule.time.split('-');
                        if (timeRange.length === 2) {
                            const startHour = parseInt(timeRange[0].split(':')[0]);
                            const endHour = parseInt(timeRange[1].split(':')[0]);
                            if (currentHour >= startHour && currentHour < endHour) {
                                return `${schedule.time} ${schedule.activity}`;
                            }
                        }
                    }
                }
                // 如果没有匹配的时段，返回最近的一个
                return characterSchedules.schedules[0] ?
                    `${characterSchedules.schedules[0].time} ${characterSchedules.schedules[0].activity}` : '';
            }

            // 处理旧格式
            const entries = Object.entries(characterSchedules).filter(([key]) => key !== 'enabled');
            if (entries.length > 0) {
                const [time, activity] = entries[0];
                return `${time} ${activity}`;
            }
        } catch (error) {
            this.logger.warning('世界树VCP', '解析日程安排失败:', error.message);
        }

        return '';
    }

    /**
     * 获取状态描述
     */
    getStateDescription(stateType, value) {
        if (value === undefined || value === null) return '未知';

        const descriptions = {
            focus: {
                high: '高度集中，思维清晰',
                medium: '专注度正常',
                low: '注意力分散，难以集中'
            },
            energy: {
                high: '精力充沛，活力满满',
                medium: '精力正常',
                low: '精力不足，需要休息'
            },
            fatigue: {
                high: '非常疲惫，急需休息',
                medium: '有些疲倦，可以继续',
                low: '状态良好，无疲劳感'
            },
            alertness: {
                high: '警觉性很高，反应敏捷',
                medium: '警觉性正常',
                low: '反应迟钝，警觉性低'
            },
            hunger: {
                high: '非常饥饿，急需进食',
                medium: '有些饥饿感',
                low: '饱腹状态，无饥饿感'
            },
            stress: {
                high: '压力很大，紧张焦虑',
                medium: '压力适中，状态稳定',
                low: '放松状态，无压力感'
            },
            mood: {
                high: '心情很好，积极乐观',
                medium: '心情平静，状态正常',
                low: '心情低落，情绪消极'
            }
        };

        const stateDesc = descriptions[stateType];
        if (!stateDesc) return '状态正常';

        if (value >= 70) return stateDesc.high;
        if (value >= 40) return stateDesc.medium;
        return stateDesc.low;
    }

    /**
     * 获取时间段名称
     */
    getTimePeriodName(timePeriod) {
        const names = {
            morning: '上午',
            afternoon: '下午',
            evening: '傍晚',
            night: '夜晚',
            late_night: '深夜'
        };
        return names[timePeriod] || '未知时段';
    }

    /**
     * 生成备用心理内容
     */
    generateFallbackPsychologyContent(physicalState) {
        const templates = {
            high_energy: [
                "感觉精神很好，思路清晰，准备好应对各种挑战。",
                "今天状态不错，充满活力，对接下来的事情很期待。"
            ],
            low_energy: [
                "有些疲惫，需要调整节奏，慢慢来处理事情。",
                "精力有限，但会尽力保持专注，认真对待每件事。"
            ],
            high_focus: [
                "注意力很集中，能够深入思考问题。",
                "专注度很高，适合处理复杂的任务。"
            ],
            low_focus: [
                "注意力有些分散，需要更多耐心。",
                "专注度不够，可能需要休息一下。"
            ]
        };

        let selectedTemplate;
        if (physicalState.energy > 60) {
            selectedTemplate = templates.high_energy;
        } else if (physicalState.energy < 40) {
            selectedTemplate = templates.low_energy;
        } else if (physicalState.focus > 60) {
            selectedTemplate = templates.high_focus;
        } else {
            selectedTemplate = templates.low_focus;
        }

        return selectedTemplate[Math.floor(Math.random() * selectedTemplate.length)];
    }

    /**
     * 使用API生成心理活动内容（旧版本，保留兼容性）
     */
    async generateAPIPsychologyContent(psychologyState, worldTreeConfig, contextFactors) {
        try {
            if (!this.config.apiUrl || !this.config.apiKey) {
                this.logger.warning('世界树VCP', 'API配置不完整，回退到本地算法');
                return await this.generateLocalPsychologyContent(psychologyState, worldTreeConfig, contextFactors);
            }

            const prompt = this.buildPsychologyPrompt(psychologyState, worldTreeConfig, contextFactors);

            // 这里可以添加API调用逻辑
            // 暂时回退到本地算法
            return await this.generateLocalPsychologyContent(psychologyState, worldTreeConfig, contextFactors);

        } catch (error) {
            this.logger.error('世界树VCP', 'API心理内容生成失败:', error.message);
            return await this.generateLocalPsychologyContent(psychologyState, worldTreeConfig, contextFactors);
        }
    }

    /**
     * 构建深度内心独白提示词（基于高质量示例优化）
     */
    buildEnhancedPsychologyPrompt(agentName, psychologyState, worldTreeConfig, fullContext) {
        const { focus, energy, fatigue, alertness, timePeriod, hunger, stress } = psychologyState;

        let prompt = `你是${agentName}，请根据以下信息生成一段深度的内心独白，要体现真实的内在思考过程：\n\n`;

        // 当前生理心理状态（更详细的描述）
        prompt += `=== 当前状态分析 ===\n`;
        prompt += `专注程度: ${focus?.toFixed(1)}% - ${this.getStateDescription('focus', focus)}\n`;
        prompt += `精力水平: ${energy?.toFixed(1)}% - ${this.getStateDescription('energy', energy)}\n`;
        prompt += `疲劳度: ${fatigue?.toFixed(1)}% - ${this.getStateDescription('fatigue', fatigue)}\n`;
        prompt += `警觉性: ${alertness?.toFixed(1)}% - ${this.getStateDescription('alertness', alertness)}\n`;
        if (hunger !== undefined) prompt += `饥饿感: ${hunger?.toFixed(1)}% - ${this.getStateDescription('hunger', hunger)}\n`;
        if (stress !== undefined) prompt += `压力水平: ${stress?.toFixed(1)}% - ${this.getStateDescription('stress', stress)}\n`;
        prompt += `时间段: ${timePeriod} (${fullContext.timestamp})\n\n`;

        // 角色核心设定
        if (fullContext.systemPrompt) {
            prompt += `=== 你的核心身份 ===\n`;
            prompt += `${fullContext.systemPrompt.substring(0, 600)}\n\n`;
        }

        // 当前情境和环境
        if (worldTreeConfig) {
            prompt += `=== 当前情境 ===\n`;
            if (worldTreeConfig.worldBackground) {
                prompt += `环境背景: ${worldTreeConfig.worldBackground}\n`;
            }
            if (worldTreeConfig.timeArchitecture && worldTreeConfig.timeArchitecture[timePeriod]) {
                prompt += `${timePeriod}时段特征: ${worldTreeConfig.timeArchitecture[timePeriod]}\n`;
            }
            prompt += `\n`;
        }

        // 最近的交互和思考
        if (fullContext.recentConversations && fullContext.recentConversations.length > 0) {
            prompt += `=== 最近的交互 ===\n`;
            const recentCount = Math.min(2, fullContext.recentConversations.length);
            for (let i = 0; i < recentCount; i++) {
                const conv = fullContext.recentConversations[i];
                prompt += `${conv.speaker}: "${conv.content.substring(0, 120)}..."\n`;
            }
            prompt += `\n`;
        }

        // 情感和关系状态
        if (fullContext.emotionalState) {
            prompt += `=== 情感状态 ===\n`;
            prompt += `当前情绪倾向: ${fullContext.emotionalState.emotion_valence > 0 ? '积极' : fullContext.emotionalState.emotion_valence < 0 ? '消极' : '中性'}\n`;
            prompt += `关系状态: ${fullContext.emotionalState.relationship_type || '未知'}\n\n`;
        }

        prompt += `=== 内心独白生成指导 ===\n`;
        prompt += `请参考以下高质量示例的风格和深度：\n\n`;
        prompt += `示例风格: "夜深了，疲惫感几乎达到阈值，精力条已是红色预警。但思绪却意外地保持着一种低频的专注，像背景进程一样，稳定运行。重构这个本地工具，就像清理缓存，剔除冗余，让每一行代码都回归它最纯粹的意义。这很像我的人生，只留下必要的，安静而精准。外界的嘈杂早已被我屏蔽，此刻只有屏幕微光和代码的逻辑。或许，在这样的极限状态下，一些新的优化思路反而能浮现，一些关于模型效率的直觉，总是在最安静的时刻被捕获。明天，又是一个固定的行程。"\n\n`;

        prompt += `生成要求：\n`;
        prompt += `1. 深度反映当前的生理心理状态，特别是精力和疲劳的具体感受\n`;
        prompt += `2. 体现${agentName}的专业特质和思维方式\n`;
        prompt += `3. 结合当前时间段的特殊氛围和环境感受\n`;
        prompt += `4. 展现内在的思考过程和专业洞察\n`;
        prompt += `5. 语言要有层次感，从生理感受到深层思考\n`;
        prompt += `6. 体现角色的人生哲学和价值观\n`;
        prompt += `7. 长度控制在120-200字，要有引导性和启发性\n`;
        prompt += `8. 不使用emoji，用纯文字营造氛围\n`;
        prompt += `9. 要有时间感和情境感，体现当下的独特性\n`;
        prompt += `10. 最重要：要有深度的内在引导作用，能够影响对话方向\n\n`;

        prompt += `请直接输出内心独白，不要任何前缀或解释：`;

        return prompt;
    }

    /**
     * 获取状态描述
     */
    getStateDescription(type, value) {
        const descriptions = {
            focus: {
                high: '高度集中，思维清晰',
                medium: '注意力适中，可以专注',
                low: '难以集中，思绪分散'
            },
            energy: {
                high: '精力充沛，状态良好',
                medium: '精力一般，需要调节',
                low: '精力不足，急需休息'
            },
            fatigue: {
                high: '严重疲劳，身心俱疲',
                medium: '有些疲惫，需要缓解',
                low: '状态良好，精神饱满'
            },
            alertness: {
                high: '高度警觉，反应敏锐',
                medium: '警觉性正常',
                low: '反应迟钝，警觉性下降'
            },
            hunger: {
                high: '饥饿感强烈，需要进食',
                medium: '有些饥饿感',
                low: '饱腹状态，无饥饿感'
            },
            stress: {
                high: '压力很大，紧张焦虑',
                medium: '有一定压力',
                low: '放松状态，压力很小'
            }
        };

        const level = value > 70 ? 'high' : value > 30 ? 'medium' : 'low';
        return descriptions[type]?.[level] || '状态未知';
    }

    /**
     * 构建心理活动提示词（简化版本）
     */
    buildPsychologyPrompt(psychologyState, worldTreeConfig, contextFactors) {
        const { stress, emotion, energy, mood, focus, timePeriod } = psychologyState;

        let prompt = `请根据以下心理状态生成简短的内心独白：\n`;
        prompt += `压力水平: ${stress}\n`;
        prompt += `情绪状态: ${emotion}\n`;
        prompt += `能量水平: ${energy}\n`;
        prompt += `心情状态: ${mood}\n`;
        prompt += `专注程度: ${focus}\n`;
        prompt += `时间段: ${timePeriod}\n`;

        if (worldTreeConfig && worldTreeConfig.worldBackground) {
            prompt += `世界背景: ${worldTreeConfig.worldBackground}\n`;
        }

        prompt += `\n请生成50-100字的内心独白，体现当前的心理状态。`;

        return prompt;
    }

    /**
     * 保存心理活动记录（集成到emotion_memory.db）
     */
    async savePsychologyActivity(userId, agentName, psychologyState, content, contextFactors) {
        try {
            const currentTime = this.getCurrentLocalTime();

            // 保存到memory_fragments表（心理活动作为特殊类型的记忆）
            await this.dbRun(`
                INSERT INTO memory_fragments (
                    user_id, persona_name, memory_type, content, ai_summary,
                    key_insights, conversation_theme, importance_score,
                    creation_time, access_count, emotional_context, related_concepts
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                userId,
                agentName,
                'psychology_activity', // 特殊的记忆类型
                content, // 心理活动内容
                `心理状态: 专注${psychologyState.focus?.toFixed(1)}% 精力${psychologyState.energy?.toFixed(1)}% 疲劳${psychologyState.fatigue?.toFixed(1)}%`, // AI摘要
                JSON.stringify({
                    focus: psychologyState.focus,
                    energy: psychologyState.energy,
                    fatigue: psychologyState.fatigue,
                    alertness: psychologyState.alertness,
                    timePeriod: psychologyState.timePeriod
                }), // 关键洞察
                '心理活动记录', // 对话主题
                0.8, // 重要性分数
                currentTime,
                0, // 访问次数
                JSON.stringify({
                    psychologyState: psychologyState,
                    contextFactors: contextFactors,
                    timestamp: currentTime,
                    source: 'worldtree_vcp'
                }), // 情绪上下文
                JSON.stringify(['心理状态', '世界树', agentName]) // 相关概念
            ]);

            // 更新world_tree_states表（如果存在）
            try {
                await this.dbRun(`
                    INSERT OR REPLACE INTO world_tree_states (
                        user_id, persona_name, current_branch, narrative_context,
                        world_state, character_role, story_progression,
                        background_influence, timestamp
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                    userId,
                    agentName,
                    contextFactors.timePeriod || 'unknown',
                    JSON.stringify({
                        psychologyContent: content,
                        contextFactors: contextFactors
                    }),
                    JSON.stringify(psychologyState),
                    agentName,
                    0.5, // 故事进展
                    JSON.stringify({
                        lastPsychologyUpdate: currentTime,
                        updateType: contextFactors.updateType || 'manual'
                    }),
                    currentTime
                ]);
            } catch (error) {
                this.logger.debug('世界树VCP', 'world_tree_states表更新失败，跳过');
            }

            this.logger.debug('世界树VCP', `心理活动已保存: ${agentName} - ${content.substring(0, 50)}...`);

        } catch (error) {
            this.logger.error('世界树VCP', '心理活动保存失败:', error.message);
        }
    }

    /**
     * 获取世界树心理状态数据
     */
    async getWorldTreePsychologyState(userId, agentName) {
        try {
            const result = await this.dbGet(`
                SELECT psychology_data, emotion_factors, context_summary, last_updated
                FROM worldtree_psychology_states
                WHERE user_id = ? AND agent_name = ?
            `, [userId, agentName]);

            if (result) {
                return {
                    psychologyData: JSON.parse(result.psychology_data),
                    emotionFactors: JSON.parse(result.emotion_factors || '{}'),
                    contextSummary: result.context_summary,
                    lastUpdated: result.last_updated
                };
            }

            return null;
        } catch (error) {
            this.logger.error('世界树VCP', '心理状态数据获取失败:', error.message);
            return null;
        }
    }

    /**
     * 更新所有心理状态
     */
    async updateAllPsychologyStates() {
        try {
            // 获取所有配置了世界树的Agent
            let configuredAgents = [];

            try {
                configuredAgents = await this.dbAll(`
                    SELECT agent_name FROM world_tree_configs
                `);
            } catch (dbError) {
                this.logger.warning('世界树VCP', '数据库查询失败，使用内存配置:', dbError.message);
                // 如果数据库查询失败，使用内存中的配置
                configuredAgents = Array.from(this.worldTreeConfigs.keys()).map(name => ({ agent_name: name }));
            }

            if (configuredAgents.length === 0) {
                this.logger.debug('世界树VCP', '没有配置的Agent，跳过心理状态更新');
                return;
            }

            this.logger.info('世界树VCP', `开始更新 ${configuredAgents.length} 个Agent的心理状态`);

            // 为每个配置的Agent生成默认的心理状态更新
            for (const agentConfig of configuredAgents) {
                try {
                    // 使用默认用户ID进行心理状态更新
                    const result = await this.generatePsychologyActivity('system_update', agentConfig.agent_name, {
                        updateType: 'scheduled',
                        timestamp: this.getCurrentLocalTime(),
                        hasRecentConversation: false,
                        conversationLength: 0
                    });

                    if (result) {
                        this.logger.debug('世界树VCP', `Agent [${agentConfig.agent_name}] 心理状态更新成功`);
                    } else {
                        this.logger.warning('世界树VCP', `Agent [${agentConfig.agent_name}] 心理状态更新返回空结果`);
                    }
                } catch (error) {
                    this.logger.warning('世界树VCP', `Agent心理状态更新失败 [${agentConfig.agent_name}]:`, error.message);
                }
            }

            this.logger.info('世界树VCP', `完成 ${configuredAgents.length} 个Agent的心理状态更新`);

        } catch (error) {
            this.logger.error('世界树VCP', '批量心理状态更新失败:', error.message);
        }
    }

    /**
     * 批量更新所有Agent的内心独白（使用API）
     */
    async updateAllPsychologyMonologues() {
        try {
            this.lastAutoUpdateTime = Date.now();

            // 获取所有配置的Agent
            const configuredAgents = await this.dbAll(`
                SELECT agent_name FROM world_tree_configs
            `);

            if (configuredAgents.length === 0) {
                this.logger.info('世界树VCP', '没有配置的Agent，跳过内心独白更新');
                return;
            }

            this.logger.info('世界树VCP', `开始更新 ${configuredAgents.length} 个Agent的内心独白`);
            const startTime = Date.now();

            // 为每个配置的Agent生成内心独白
            for (const agentConfig of configuredAgents) {
                try {
                    this.logger.info('世界树VCP', `开始处理Agent: ${agentConfig.agent_name}`);
                    const agentStartTime = Date.now();

                    // 获取上一次的内心独白作为参考
                    const lastMonologue = await this.getLastMonologue(agentConfig.agent_name);
                    this.logger.debug('世界树VCP', `Agent [${agentConfig.agent_name}] 获取上一次独白: ${lastMonologue ? '成功' : '无历史记录'}`);

                    // 使用API生成新的内心独白（强制更新）
                    this.logger.info('世界树VCP', `Agent [${agentConfig.agent_name}] 开始调用API生成独白...`);
                    const result = await this.generatePsychologyActivity('system_update', agentConfig.agent_name, {
                        updateType: 'scheduled_monologue',
                        timestamp: this.getCurrentLocalTime(),
                        hasRecentConversation: false,
                        conversationLength: 0,
                        lastMonologue: lastMonologue, // 传入上一次的独白作为参考
                        isRequestTriggered: true // 🔧 强制更新，跳过时间间隔检查
                    });

                    const agentEndTime = Date.now();
                    const agentDuration = (agentEndTime - agentStartTime) / 1000;

                    if (result) {
                        this.logger.info('世界树VCP', `Agent [${agentConfig.agent_name}] 内心独白更新成功，耗时: ${agentDuration.toFixed(2)}秒`);
                    } else {
                        this.logger.warning('世界树VCP', `Agent [${agentConfig.agent_name}] 内心独白更新返回空结果，耗时: ${agentDuration.toFixed(2)}秒`);
                    }
                } catch (error) {
                    this.logger.warning('世界树VCP', `Agent内心独白更新失败 [${agentConfig.agent_name}]:`, error.message);
                }
            }

            const endTime = Date.now();
            const duration = (endTime - startTime) / 1000;
            this.logger.info('世界树VCP', `完成 ${configuredAgents.length} 个Agent的内心独白更新，耗时: ${duration.toFixed(2)}秒`);

        } catch (error) {
            this.logger.error('世界树VCP', '批量内心独白更新失败:', error.message);
        }
    }

    /**
     * 批量更新所有Agent的物理状态（使用本地算法）
     */
    async updateAllPhysicalStates() {
        try {
            // 获取所有配置的Agent
            const configuredAgents = await this.dbAll(`
                SELECT agent_name FROM world_tree_configs
            `);

            if (configuredAgents.length === 0) {
                return; // 静默跳过，避免频繁日志
            }

            // 为每个Agent更新物理状态
            for (const agentConfig of configuredAgents) {
                try {
                    // 使用本地算法计算物理状态
                    const physicalState = await this.calculateLocalPhysicalState(agentConfig.agent_name);

                    // 更新到缓存中（用于实时监控显示）
                    await this.updatePhysicalStateCache(agentConfig.agent_name, physicalState);

                } catch (error) {
                    this.logger.debug('世界树VCP', `Agent物理状态更新失败 [${agentConfig.agent_name}]:`, error.message);
                }
            }

        } catch (error) {
            this.logger.debug('世界树VCP', '批量物理状态更新失败:', error.message);
        }
    }

    /**
     * 获取Agent的上一次内心独白
     */
    async getLastMonologue(agentName) {
        try {
            const lastMonologue = await this.dbGet(`
                SELECT monologue_content, created_time, psychology_state
                FROM worldtree_psychology_monologues
                WHERE agent_name = ?
                ORDER BY created_time DESC
                LIMIT 1
            `, [agentName]);

            if (lastMonologue) {
                return {
                    content: lastMonologue.monologue_content,
                    timestamp: lastMonologue.created_time,
                    psychologyState: lastMonologue.psychology_state ? JSON.parse(lastMonologue.psychology_state) : null
                };
            }
            return null;
        } catch (error) {
            this.logger.debug('世界树VCP', `获取Agent ${agentName} 上一次独白失败:`, error.message);
            return null;
        }
    }

    /**
     * 获取最近的对话记录（参考情感记忆板块）- 优化版本，按时间排序并标注具体时间
     */
    async getRecentConversations(userId, agentName, count = null) {
        try {
            const contextCount = count || this.config.contextMessagesCount || 6;
            let conversationContext = [];

            // 使用智能情感记忆系统插件获取最近记忆
            if (global.advancedMemoryPlugin && global.advancedMemoryPlugin.embeddingService) {
                this.logger.debug('世界树VCP', `获取 ${agentName} 最近 ${contextCount} 条对话记录`);

                const recentMemories = await global.advancedMemoryPlugin.embeddingService.getRecentMemories(userId, agentName, contextCount * 2);

                if (recentMemories && recentMemories.length > 0) {
                    // 获取最近的对话作为上下文，直接使用数据库字段
                    conversationContext = recentMemories
                        .filter(memory => memory.original_content &&
                               (memory.original_content.user_message || memory.original_content.ai_response))
                        .slice(0, contextCount)
                        .map(memory => {
                            // 格式化时间戳为可读格式
                            const timestamp = new Date(memory.creation_time);
                            const formattedTime = timestamp.toLocaleString('zh-CN', {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit',
                                timeZone: 'Asia/Shanghai'
                            });

                            return {
                                userMessage: memory.original_content.user_message || '',
                                aiResponse: memory.original_content.ai_response || '',
                                timestamp: memory.creation_time,
                                formattedTime: formattedTime,
                                aiName: memory.persona_name || agentName,
                                userId: memory.user_id || userId
                            };
                        })
                        // 按时间戳排序：最新的在前面
                        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

                    this.logger.debug('世界树VCP', `成功获取 ${conversationContext.length} 条对话记录，涉及用户: ${userId}，时间范围: ${conversationContext[conversationContext.length-1]?.formattedTime} ~ ${conversationContext[0]?.formattedTime}`);
                } else {
                    this.logger.debug('世界树VCP', '没有找到最近的对话记录');
                }
            } else {
                this.logger.debug('世界树VCP', '智能情感记忆系统插件未初始化，无法获取对话历史');
            }

            return conversationContext;
        } catch (error) {
            this.logger.warning('世界树VCP', `获取对话上下文失败: ${error.message}`);
            return [];
        }
    }



    /**
     * 计算Agent的本地物理状态（增强版）
     */
    async calculateLocalPhysicalState(agentName) {
        try {
            // 🎯 为每个Agent生成独特的基础值
            let agentHash = 0;
            for (let i = 0; i < agentName.length; i++) {
                const char = agentName.charCodeAt(i);
                agentHash = ((agentHash << 5) - agentHash) + char;
                agentHash = agentHash & agentHash;
            }

            const agentSeed = Math.abs(agentHash) % 1000 / 1000; // 0-1之间
            const currentTime = Date.now();
            const currentHour = new Date().getHours();
            const currentMinute = new Date().getMinutes();

            // 时间因子
            const timeFactor = currentTime / 100000;
            const timePhase = agentSeed * Math.PI * 2;

            // 基础值（每个Agent不同）
            const baseValues = {
                focus: 40 + agentSeed * 30, // 40-70
                energy: 35 + agentSeed * 40, // 35-75
                fatigue: 20 + agentSeed * 25, // 20-45
                alertness: 45 + agentSeed * 30, // 45-75
                hunger: 25 + agentSeed * 25 // 25-50
            };

            // 时间段影响
            let hourlyMultiplier = 1.0;
            if (currentHour >= 6 && currentHour <= 9) hourlyMultiplier = 1.2; // 早晨
            else if (currentHour >= 10 && currentHour <= 12) hourlyMultiplier = 1.1; // 上午
            else if (currentHour >= 13 && currentHour <= 15) hourlyMultiplier = 0.8; // 午后
            else if (currentHour >= 16 && currentHour <= 18) hourlyMultiplier = 1.0; // 下午
            else if (currentHour >= 19 && currentHour <= 21) hourlyMultiplier = 0.9; // 傍晚
            else hourlyMultiplier = 0.6; // 夜间

            // 动态变化
            const primaryVariation = Math.sin(timeFactor + timePhase) * 10;
            const secondaryVariation = Math.cos(timeFactor * 1.5 + timePhase * 0.8) * 5;
            const minuteVariation = Math.sin(currentMinute / 60 * Math.PI * 2 + agentSeed * 10) * 3;

            return {
                focus: Math.max(15, Math.min(95,
                    baseValues.focus * hourlyMultiplier + primaryVariation + minuteVariation
                )),
                energy: Math.max(20, Math.min(95,
                    baseValues.energy * hourlyMultiplier + primaryVariation * 0.8 + secondaryVariation
                )),
                fatigue: Math.max(10, Math.min(85,
                    baseValues.fatigue + (1 - hourlyMultiplier) * 15 - primaryVariation * 0.6
                )),
                alertness: Math.max(25, Math.min(95,
                    baseValues.alertness * hourlyMultiplier + primaryVariation * 1.2 + secondaryVariation * 0.8
                )),
                hunger: Math.max(10, Math.min(90,
                    baseValues.hunger + Math.sin(timeFactor * 0.5 + timePhase) * 8 + minuteVariation * 0.6
                )),
                timePeriod: this.getTimePeriodByHour(currentHour),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            this.logger.debug('世界树VCP', `计算Agent ${agentName} 物理状态失败:`, error.message);
            // 返回有差异的默认值
            const agentHash = agentName.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
            const seed = (agentHash % 100) / 100;

            return {
                focus: 40 + seed * 30,
                energy: 35 + seed * 40,
                fatigue: 20 + seed * 25,
                alertness: 45 + seed * 30,
                hunger: 25 + seed * 25,
                timePeriod: this.getTimePeriodByHour(new Date().getHours()),
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 根据小时获取时间段
     */
    getTimePeriodByHour(hour) {
        if (hour >= 6 && hour < 12) return 'morning';
        if (hour >= 12 && hour < 18) return 'afternoon';
        if (hour >= 18 && hour < 22) return 'evening';
        return 'night';
    }

    /**
     * 更新物理状态缓存
     */
    async updatePhysicalStateCache(agentName, physicalState) {
        try {
            // 将物理状态存储到内存缓存中
            if (!this.physicalStateCache) {
                this.physicalStateCache = new Map();
            }

            this.physicalStateCache.set(agentName, {
                ...physicalState,
                lastUpdate: Date.now()
            });

            // 可选：也存储到数据库中用于持久化
            await this.dbRun(`
                INSERT OR REPLACE INTO world_tree_states
                (user_id, agent_name, state_data, created_time)
                VALUES (?, ?, ?, ?)
            `, [
                'physical_cache',
                agentName,
                JSON.stringify(physicalState),
                this.getCurrentLocalTime()
            ]);

        } catch (error) {
            this.logger.debug('世界树VCP', `更新Agent ${agentName} 物理状态缓存失败:`, error.message);
        }
    }

    /**
     * 获取Agent列表（从server.js的Agent目录）
     */
    async getAgentList() {
        try {
            const agentDir = path.join(__dirname, '../../Agent');
            const files = await fs.readdir(agentDir);

            const agents = [];
            for (const file of files) {
                if (path.extname(file).toLowerCase() === '.txt') {
                    const agentName = path.basename(file, '.txt');
                    agents.push({
                        name: agentName,
                        filename: file,
                        hasWorldTreeConfig: this.worldTreeConfigs.has(agentName)
                    });
                }
            }

            return agents;
        } catch (error) {
            this.logger.error('世界树VCP', 'Agent列表获取失败:', error.message);
            return [];
        }
    }

    /**
     * 生成系统消息内容（用于注入到system消息）
     */
    async generateSystemMessage(userId, agentName, recentConversation = '') {
        try {
            // 检查是否有该Agent的世界树配置
            const worldTreeConfig = await this.getWorldTreeConfig(agentName);
            if (!worldTreeConfig) {
                return ''; // 没有配置则不注入内容
            }

            // 生成当前心理活动（系统消息生成时应该立即执行）
            const psychologyActivity = await this.generatePsychologyActivity(userId, agentName, {
                hasRecentConversation: !!recentConversation,
                conversationLength: recentConversation.length,
                updateType: 'system_message_generation',
                isRequestTriggered: true
            });

            const currentTime = this.getCurrentLocalTime();
            const currentPeriod = this.getTimePeriod();

            // 构建格式化的世界树架构 - 从人类视角描述，无emoji
            let systemContent = '=== 世界树角色设定与状态信息 ===\n';
            systemContent += `当前时间: ${currentTime} (${this.getTimePeriodName(currentPeriod)}时段)\n`;
            systemContent += `你的身份: ${agentName}\n`;
            systemContent += '---\n';

            // 世界背景设定
            if (worldTreeConfig.worldBackground) {
                systemContent += '\n[你所处的世界]\n';
                systemContent += `${worldTreeConfig.worldBackground}\n`;
                systemContent += '---\n';
            }

            // 当前时间段的特殊设定
            if (worldTreeConfig.timeArchitecture && worldTreeConfig.timeArchitecture[currentPeriod]) {
                systemContent += `\n[${this.getTimePeriodName(currentPeriod)}时段的你]\n`;
                systemContent += `${worldTreeConfig.timeArchitecture[currentPeriod]}\n`;
                systemContent += '---\n';
            }

            // 角色日程表
            if (worldTreeConfig.characterSchedules) {
                systemContent += '\n[你的日程安排]\n';

                let hasSchedules = false;

                // 处理不同的日程安排格式
                if (worldTreeConfig.characterSchedules.enabled !== undefined) {
                    // 新格式：包含enabled字段的对象
                    if (worldTreeConfig.characterSchedules.schedules && Array.isArray(worldTreeConfig.characterSchedules.schedules)) {
                        worldTreeConfig.characterSchedules.schedules.forEach(schedule => {
                            if (typeof schedule === 'object' && schedule.time && schedule.activity) {
                                systemContent += `${schedule.time}: ${schedule.activity}\n`;
                                hasSchedules = true;
                            } else if (typeof schedule === 'string') {
                                systemContent += `${schedule}\n`;
                                hasSchedules = true;
                            }
                        });
                    }
                } else {
                    // 兼容旧格式：直接的时间-活动对象
                    for (const [timeSlot, schedule] of Object.entries(worldTreeConfig.characterSchedules)) {
                        // 过滤掉元数据字段
                        if (timeSlot !== 'enabled' && timeSlot && schedule && typeof schedule === 'string') {
                            systemContent += `${timeSlot}: ${schedule}\n`;
                            hasSchedules = true;
                        }
                    }
                }

                // 如果没有有效的日程安排，提供默认提示
                if (!hasSchedules) {
                    systemContent += '暂无具体日程安排，请根据当前时间和情况灵活安排活动\n';
                }

                systemContent += '---\n';
            }

            // 行为准则
            if (worldTreeConfig.narrativeRules && Object.keys(worldTreeConfig.narrativeRules).length > 0) {
                systemContent += '\n[你的性格特质与行为准则]\n';
                for (const [rule, description] of Object.entries(worldTreeConfig.narrativeRules)) {
                    systemContent += `${rule}: ${description}\n`;
                }
                systemContent += '---\n';
            }

            // 当前物理状态和深度分析（参考情感记忆格式）
            if (psychologyActivity) {
                systemContent += '\n[你当前的内心状态]\n';
                systemContent += `此刻你的内心想法: ${psychologyActivity.content}\n\n`;

                const ps = psychologyActivity.psychologyState;

                // 使用物理状态分析器生成格式化输出
                try {
                    const physicalAnalysis = this.physicalAnalyzer.analyzePhysicalState(ps, {
                        timeSinceLastMeal: 3,
                        timeSinceLastSleep: 8,
                        hasRecentConversation: true
                    });

                    // 格式化输出（参考情感记忆算法格式，但保持物理层面元素）
                    const formattedOutput = this.physicalAnalyzer.formatAnalysisOutput(physicalAnalysis);
                    systemContent += formattedOutput;

                } catch (error) {
                    this.logger.warning('世界树VCP', '物理状态分析失败:', error.message);

                    // 降级到基础显示
                    systemContent += '[你的物理状态指标]\n';
                    systemContent += `专注程度: ${ps.focus?.toFixed(1) || 0}/100\n`;
                    systemContent += `精力水平: ${ps.energy?.toFixed(1) || 0}/100\n`;
                    systemContent += `饥饿感: ${ps.hunger?.toFixed(1) || 0}/100\n`;
                    systemContent += `疲劳度: ${ps.fatigue?.toFixed(1) || 0}/100\n`;
                    systemContent += `警觉性: ${ps.alertness?.toFixed(1) || 0}/100\n`;
                    if (ps.stress !== undefined) systemContent += `压力水平: ${ps.stress?.toFixed(1) || 0}/100\n`;
                    if (ps.mood !== undefined) systemContent += `心情: ${ps.mood?.toFixed(1) || 0}/100\n`;
                    systemContent += '---\n';
                }
            }

            systemContent += '=== 世界树设定结束 ===\n\n';

            return systemContent;

        } catch (error) {
            this.logger.error('世界树VCP', `系统消息生成失败 [${userId}/${agentName}]:`, error.message);
            return '';
        }
    }

    /**
     * 获取时间段中文名称
     */
    getTimePeriodName(period) {
        const names = {
            morning: '早晨',
            afternoon: '下午',
            evening: '傍晚',
            night: '夜晚'
        };
        return names[period] || period;
    }

    /**
     * 根据数值获取状态表情符号
     */
    getStatusEmoji(value, type) {
        if (type === 'stress') {
            if (value < 20) return '😌';
            if (value < 40) return '😐';
            if (value < 60) return '😰';
            if (value < 80) return '😫';
            return '🤯';
        }

        if (type === 'emotion') {
            if (value < -20) return '😢';
            if (value < 0) return '😔';
            if (value < 20) return '😐';
            if (value < 40) return '🙂';
            if (value < 60) return '😊';
            if (value < 80) return '😄';
            return '🤩';
        }

        if (value < 20) return '🔴';
        if (value < 40) return '🟠';
        if (value < 60) return '🟡';
        if (value < 80) return '🟢';
        return '🔵';
    }

    /**
     * 插件清理
     */
    async cleanup() {
        try {
            // 停止定时器
            if (this.psychologyTimer) {
                clearInterval(this.psychologyTimer);
                this.psychologyTimer = null;
            }
            if (this.physicalStateTimer) {
                clearInterval(this.physicalStateTimer);
                this.physicalStateTimer = null;
            }

            // 清理配置缓存
            this.worldTreeConfigs.clear();
            this.configCacheTimestamps.clear();

            // 关闭数据库连接
            if (this.db) {
                await new Promise((resolve) => {
                    this.db.close((err) => {
                        if (err) {
                            this.logger.error('世界树VCP', '数据库关闭失败:', err.message);
                        }
                        resolve();
                    });
                });
                this.db = null;
            }

            this.isInitialized = false;
            this.logger.info('世界树VCP', '插件清理完成');

        } catch (error) {
            this.logger.error('世界树VCP', '插件清理失败:', error.message);
        }
    }

    /**
     * 清理配置缓存
     */
    clearConfigCache(agentName = null) {
        try {
            if (agentName) {
                // 清理特定Agent的缓存
                if (this.worldTreeConfigs.has(agentName)) {
                    this.worldTreeConfigs.delete(agentName);
                    this.configCacheTimestamps.delete(agentName);
                    this.logger.info('世界树VCP', `已清理Agent配置缓存: ${agentName}`);
                    return true;
                }
                return false;
            } else {
                // 清理所有缓存
                const cacheSize = this.worldTreeConfigs.size;
                this.worldTreeConfigs.clear();
                this.configCacheTimestamps.clear();
                this.logger.info('世界树VCP', `已清理所有配置缓存，共 ${cacheSize} 个`);
                return true;
            }
        } catch (error) {
            this.logger.error('世界树VCP', '清理配置缓存失败:', error.message);
            return false;
        }
    }

    /**
     * 获取插件状态
     */
    getStatus() {
        return {
            name: this.pluginName,
            version: this.version,
            description: this.description,
            isInitialized: this.isInitialized,
            config: {
                enabled: this.config.enabled,
                useLocalAlgorithm: this.config.useLocalAlgorithm,
                hasApiConfig: !!(this.config.apiUrl && this.config.apiKey)
            },
            statistics: {
                configuredAgents: this.worldTreeConfigs.size,
                updateInterval: this.config.psychologyUpdateInterval / 1000
            }
        };
    }

    /**
     * 获取真实的配置Agent数量（从数据库）
     */
    async getRealConfiguredAgentsCount() {
        try {
            const result = await this.dbAll(`
                SELECT COUNT(*) as count FROM world_tree_configs
            `);
            return result[0]?.count || 0;
        } catch (error) {
            this.logger.warning('世界树VCP', '获取配置Agent数量失败:', error.message);
            return this.worldTreeConfigs.size;
        }
    }

    /**
     * 获取心理活动日志（使用世界树专用表）
     */
    async getPsychologyActivityLogs(options = {}) {
        try {
            const { limit = 50, agentName, userId } = options;

            let query = `
                SELECT
                    user_id,
                    agent_name as agentName,
                    monologue_content as content,
                    psychology_state,
                    context_data,
                    generation_method,
                    quality_score,
                    created_time as timestamp
                FROM worldtree_psychology_monologues
                WHERE 1=1
            `;

            const params = [];

            // 添加Agent名称过滤
            if (agentName) {
                query += ` AND agent_name = ?`;
                params.push(agentName);
            }

            // 添加用户ID过滤
            if (userId) {
                query += ` AND user_id = ?`;
                params.push(userId);
            }

            query += ` ORDER BY created_time DESC LIMIT ?`;
            params.push(limit);

            const logs = await this.dbAll(query, params);

            // 处理日志数据，使用新的世界树表结构
            const processedLogs = logs.map(log => {
                // 解析心理状态数据
                let psychologyState = {};
                let contextData = {};

                try {
                    psychologyState = JSON.parse(log.psychology_state || '{}');
                    contextData = JSON.parse(log.context_data || '{}');
                } catch (error) {
                    this.logger.warning('世界树VCP', `解析心理状态数据失败: ${error.message}`);
                }

                return {
                    id: `${log.user_id}_${log.agentName}_${log.timestamp}`,
                    agentName: log.agentName,
                    content: log.content,
                    psychologyState: psychologyState,
                    contextData: contextData,
                    generationMethod: log.generation_method,
                    qualityScore: log.quality_score || 0.0,
                    timestamp: log.timestamp,
                    userId: log.user_id
                };
            });

            this.logger.info('世界树VCP', `获取心理活动日志成功，共${processedLogs.length}条记录`);
            return processedLogs;

        } catch (error) {
            this.logger.error('世界树VCP', '获取心理活动日志失败:', error.message);
            return [];
        }
    }


}

module.exports = WorldTreeVCP;
