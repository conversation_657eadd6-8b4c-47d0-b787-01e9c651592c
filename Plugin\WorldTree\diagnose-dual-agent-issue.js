/**
 * 诊断双Agent心理活动问题
 * 检查为什么设置了两个世界树但只有一个Agent生效
 */

const path = require('path');
const fs = require('fs').promises;

async function diagnoseDualAgentIssue() {
    console.log('🔍 诊断双Agent心理活动问题...\n');
    
    let worldTreeVCP = null;
    
    try {
        // 1. 初始化世界树VCP插件
        console.log('1. 初始化世界树VCP插件...');
        const WorldTreeVCP = require('./WorldTreeVCP.js');
        worldTreeVCP = new WorldTreeVCP();
        
        const mockLogger = {
            info: (tag, ...args) => console.log(`[INFO] ${tag}:`, ...args),
            warning: (tag, ...args) => console.log(`[WARN] ${tag}:`, ...args),
            error: (tag, ...args) => console.log(`[ERROR] ${tag}:`, ...args),
            debug: (tag, ...args) => console.log(`[DEBUG] ${tag}:`, ...args)
        };
        
        const initResult = await worldTreeVCP.initialize(mockLogger);
        if (!initResult) {
            throw new Error('插件初始化失败');
        }
        console.log('✅ 插件初始化成功\n');
        
        // 2. 检查数据库中的世界树配置
        console.log('2. 检查数据库中的世界树配置...');
        try {
            const dbConfigs = await worldTreeVCP.dbAll(`
                SELECT agent_name, created_time, updated_time,
                       CASE WHEN config_data != '{}' THEN '✅' ELSE '❌' END as has_config,
                       CASE WHEN time_architecture != '{}' THEN '✅' ELSE '❌' END as has_time_arch,
                       CASE WHEN character_schedules != '{}' THEN '✅' ELSE '❌' END as has_schedules,
                       CASE WHEN world_background != '' THEN '✅' ELSE '❌' END as has_background,
                       CASE WHEN narrative_rules != '{}' THEN '✅' ELSE '❌' END as has_rules
                FROM world_tree_configs 
                ORDER BY updated_time DESC
            `);
            
            console.log(`📊 数据库中有 ${dbConfigs.length} 个Agent配置:`);
            if (dbConfigs.length === 0) {
                console.log('❌ 没有找到任何世界树配置！');
                console.log('请在管理界面为Agent创建世界树配置。');
                return;
            }
            
            dbConfigs.forEach((config, index) => {
                console.log(`\n${index + 1}. Agent: ${config.agent_name}`);
                console.log(`   创建时间: ${config.created_time}`);
                console.log(`   更新时间: ${config.updated_time}`);
                console.log(`   基础配置: ${config.has_config}`);
                console.log(`   时间架构: ${config.has_time_arch}`);
                console.log(`   角色日程: ${config.has_schedules}`);
                console.log(`   世界背景: ${config.has_background}`);
                console.log(`   叙事规则: ${config.has_rules}`);
            });
        } catch (error) {
            console.log(`❌ 数据库查询失败: ${error.message}`);
            return;
        }
        console.log('');
        
        // 3. 检查Agent文件存在性
        console.log('3. 检查Agent文件存在性...');
        const agentDir = path.join(__dirname, '../../Agent');
        const dbConfigs = await worldTreeVCP.dbAll(`SELECT agent_name FROM world_tree_configs`);
        
        for (const config of dbConfigs) {
            const agentFilePath = path.join(agentDir, `${config.agent_name}.txt`);
            try {
                await fs.access(agentFilePath);
                console.log(`✅ ${config.agent_name}.txt 文件存在`);
            } catch (error) {
                console.log(`❌ ${config.agent_name}.txt 文件不存在`);
                console.log(`   路径: ${agentFilePath}`);
            }
        }
        console.log('');
        
        // 4. 测试每个Agent的心理活动生成
        console.log('4. 测试每个Agent的心理活动生成...');
        const testResults = [];
        
        for (const config of dbConfigs) {
            console.log(`\n🧠 测试Agent: ${config.agent_name}`);
            
            try {
                // 测试心理状态计算
                const psychologyState = await worldTreeVCP.calculatePsychologyState('test_user', config.agent_name, {
                    hasRecentConversation: true,
                    conversationLength: 100
                });
                
                if (psychologyState) {
                    console.log(`✅ 心理状态计算成功`);
                    console.log(`   专注度: ${psychologyState.focus?.toFixed(1)}%`);
                    console.log(`   精力: ${psychologyState.energy?.toFixed(1)}%`);
                    console.log(`   疲劳: ${psychologyState.fatigue?.toFixed(1)}%`);
                    console.log(`   警觉: ${psychologyState.alertness?.toFixed(1)}%`);
                } else {
                    console.log(`❌ 心理状态计算失败`);
                }
                
                // 测试心理活动生成
                const result = await worldTreeVCP.generatePsychologyActivity('test_user', config.agent_name, {
                    hasRecentConversation: true,
                    conversationLength: 100,
                    updateType: 'manual_test',
                    isRequestTriggered: true
                });
                
                if (result) {
                    console.log(`✅ 心理活动生成成功`);
                    console.log(`   内容: "${result.content?.substring(0, 80)}..."`);
                    console.log(`   来源: ${result.source}`);
                    console.log(`   时间: ${result.timestamp}`);
                    testResults.push({ agent: config.agent_name, success: true, result });
                } else {
                    console.log(`❌ 心理活动生成失败 - 返回空结果`);
                    testResults.push({ agent: config.agent_name, success: false, error: '返回空结果' });
                }
            } catch (error) {
                console.log(`❌ 心理活动生成异常: ${error.message}`);
                testResults.push({ agent: config.agent_name, success: false, error: error.message });
            }
        }
        console.log('');
        
        // 5. 检查心理活动日志记录
        console.log('5. 检查心理活动日志记录...');
        try {
            const logs = await worldTreeVCP.dbAll(`
                SELECT agent_name, 
                       COUNT(*) as total_count,
                       COUNT(CASE WHEN created_time > datetime('now', '-1 hour') THEN 1 END) as recent_count,
                       MAX(created_time) as latest_time,
                       generation_method
                FROM psychology_monologue 
                GROUP BY agent_name, generation_method
                ORDER BY agent_name, total_count DESC
            `);
            
            if (logs.length === 0) {
                console.log('❌ 没有找到任何心理活动日志记录');
            } else {
                console.log('📊 心理活动日志统计:');
                const agentStats = {};
                
                logs.forEach(log => {
                    if (!agentStats[log.agent_name]) {
                        agentStats[log.agent_name] = { total: 0, recent: 0, latest: null, methods: [] };
                    }
                    agentStats[log.agent_name].total += log.total_count;
                    agentStats[log.agent_name].recent += log.recent_count;
                    agentStats[log.agent_name].methods.push(`${log.generation_method}(${log.total_count})`);
                    if (!agentStats[log.agent_name].latest || log.latest_time > agentStats[log.agent_name].latest) {
                        agentStats[log.agent_name].latest = log.latest_time;
                    }
                });
                
                Object.entries(agentStats).forEach(([agentName, stats]) => {
                    console.log(`\n  ${agentName}:`);
                    console.log(`    总记录: ${stats.total} 条`);
                    console.log(`    最近1小时: ${stats.recent} 条`);
                    console.log(`    最新记录: ${stats.latest}`);
                    console.log(`    生成方法: ${stats.methods.join(', ')}`);
                });
            }
        } catch (error) {
            console.log(`❌ 心理活动日志查询失败: ${error.message}`);
        }
        console.log('');
        
        // 6. 检查定时更新机制
        console.log('6. 检查定时更新机制...');
        console.log(`定时器状态: ${worldTreeVCP.psychologyTimer ? '✅ 运行中' : '❌ 未启动'}`);
        console.log(`更新间隔: ${worldTreeVCP.config.psychologyUpdateInterval / 1000} 秒`);
        console.log(`上次自动更新: ${worldTreeVCP.lastAutoUpdateTime ? new Date(worldTreeVCP.lastAutoUpdateTime).toLocaleString() : '从未'}`);
        console.log(`上次请求更新: ${worldTreeVCP.lastRequestTime ? new Date(worldTreeVCP.lastRequestTime).toLocaleString() : '从未'}`);
        
        // 手动触发一次批量更新
        console.log('\n🔄 手动触发批量心理状态更新...');
        try {
            await worldTreeVCP.updateAllPsychologyStates();
            console.log('✅ 批量更新完成');
        } catch (error) {
            console.log(`❌ 批量更新失败: ${error.message}`);
        }
        console.log('');
        
        // 7. 诊断结论和建议
        console.log('7. 诊断结论和建议...');
        const successfulAgents = testResults.filter(r => r.success).map(r => r.agent);
        const failedAgents = testResults.filter(r => !r.success);
        
        console.log('📊 测试结果总结:');
        console.log(`  配置的Agent: ${dbConfigs.length} 个`);
        console.log(`  测试成功: ${successfulAgents.length} 个 (${successfulAgents.join(', ')})`);
        console.log(`  测试失败: ${failedAgents.length} 个`);
        
        if (failedAgents.length > 0) {
            console.log('\n❌ 失败的Agent:');
            failedAgents.forEach(failed => {
                console.log(`  - ${failed.agent}: ${failed.error}`);
            });
        }
        
        if (successfulAgents.length === 0) {
            console.log('\n🔧 修复建议:');
            console.log('  1. 检查API配置是否正确');
            console.log('  2. 检查Agent文件是否存在');
            console.log('  3. 检查世界树配置是否完整');
            console.log('  4. 查看服务器日志获取详细错误信息');
        } else if (successfulAgents.length < dbConfigs.length) {
            console.log('\n⚠️ 部分Agent工作异常:');
            console.log('  1. 检查失败Agent的配置完整性');
            console.log('  2. 确认Agent文件存在且格式正确');
            console.log('  3. 检查API调用是否有限制或错误');
        } else {
            console.log('\n✅ 所有Agent都正常工作！');
            console.log('如果在实际使用中仍有问题，可能是:');
            console.log('  1. 定时更新间隔太长，需要等待');
            console.log('  2. 前端显示问题，检查浏览器控制台');
            console.log('  3. 数据库同步问题，重启服务器试试');
        }
        
    } catch (error) {
        console.error('❌ 诊断过程中发生错误:', error.message);
        console.error('错误堆栈:', error.stack);
    } finally {
        // 清理资源
        if (worldTreeVCP && worldTreeVCP.psychologyTimer) {
            clearInterval(worldTreeVCP.psychologyTimer);
        }
        if (worldTreeVCP && worldTreeVCP.db) {
            worldTreeVCP.db.close();
        }
    }
}

// 运行诊断
if (require.main === module) {
    diagnoseDualAgentIssue().then(() => {
        console.log('\n🎯 诊断完成！');
        process.exit(0);
    }).catch(error => {
        console.error('诊断失败:', error);
        process.exit(1);
    });
}

module.exports = { diagnoseDualAgentIssue };
