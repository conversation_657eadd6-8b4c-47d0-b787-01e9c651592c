# 世界树VCP插件配置文件示例
# 复制此文件为 config.env 并根据需要修改配置

# ===== 🌳 基础配置 =====
WORLDTREE_ENABLED=true
WORLDTREE_USE_LOCAL_ALGORITHM=true

# ===== 🔗 API配置（可选，如果不使用本地算法）=====
WORLDTREE_API_URL=
WORLDTREE_API_KEY=
WORLDTREE_MODEL=gpt-4o-mini

# ===== ⏰ 时间与更新配置 =====
# 心理状态更新间隔（毫秒，范围：60000-3600000）
WORLDTREE_PSYCHOLOGY_UPDATE_INTERVAL=300000

# 缓存最大存活时间（毫秒，范围：300000-7200000）
WORLDTREE_MAX_CACHE_AGE=1800000

# API请求超时时间（毫秒，范围：30000-600000）
WORLDTREE_TIMEOUT=120000

# 时间格式（local/utc/iso）
WORLDTREE_TIME_FORMAT=local

# ===== 🧠 算法权重配置 =====
# 各项心理因素权重（范围：0.0-1.0，总和建议为1.0）
WORLDTREE_STRESS_WEIGHT=0.3
WORLDTREE_EMOTION_WEIGHT=0.25
WORLDTREE_ENERGY_WEIGHT=0.2
WORLDTREE_MOOD_WEIGHT=0.15
WORLDTREE_FOCUS_WEIGHT=0.1

# ===== 🌅 时间因素配置 =====
# 不同时段的能量因子
WORLDTREE_MORNING_ENERGY_FACTOR=1.2
WORLDTREE_AFTERNOON_ENERGY_FACTOR=0.9
WORLDTREE_EVENING_ENERGY_FACTOR=0.7
WORLDTREE_NIGHT_ENERGY_FACTOR=0.5

# ===== 🎨 显示配置 =====
# 是否显示精力水平
WORLDTREE_SHOW_ENERGY=true

# 是否显示心理状态详情
WORLDTREE_SHOW_PSYCHOLOGY_DETAILS=true

# 是否显示时间架构信息
WORLDTREE_SHOW_TIME_ARCHITECTURE=true

# 是否显示角色日程表
WORLDTREE_SHOW_CHARACTER_SCHEDULES=true

# ===== 📊 日志配置 =====
# 启用调试模式
WORLDTREE_DEBUG_MODE=false

# 日志级别（debug/info/warn/error）
WORLDTREE_LOG_LEVEL=info

# ===== 📚 数据库配置 =====
# 数据库配置（自动使用AdvancedMemorySystem的数据库）
# 无需手动配置数据库路径
