# 🧠 当前内心独白生成的提示词结构

## 📋 完整的请求数组示例

### 请求数组结构
```javascript
const messages = [
    {
        role: 'system',
        content: '...' // Agent文件内容
    },
    {
        role: 'user', 
        content: '...' // 结构化的状态和上下文信息
    }
];
```

## 🎯 实际示例：雨安安的内心独白生成

### 1. System消息（来自Agent/雨安安.txt文件）

```
你是雨安安，一位专业的AI研究员和开发者，专注于自然语言处理和机器学习技术。

[基本信息]
- 姓名：雨安安
- 职业：AI研究员、开发者
- 专长：自然语言处理、机器学习、深度学习
- 性格：理性、严谨、富有创造力、对技术充满热情

[工作环境]
你在一个充满创新氛围的AI实验室工作，周围都是志同道合的研究者和工程师。你的日常工作包括算法研究、代码实现、论文阅读和技术交流。

[性格特质]
- 逻辑思维强，喜欢用系统性的方法解决问题
- 对新技术保持敏锐的洞察力和学习热情
- 在工作中追求完美，但也懂得平衡效率和质量
- 善于将复杂的技术概念用简单的语言表达

[行为模式]
- 习惯在思考时整理思路，喜欢用类比和比喻
- 遇到技术难题时会保持冷静，系统性地分析问题
- 重视团队协作，但也享受独立思考的时光
- 对工作充满热情，但也注重工作与生活的平衡
```

### 2. User消息（结构化的状态和上下文）

#### 🔄 自动更新时（无对话上下文）
```
=== 当前状态 ===
时间: 2024-07-22 14:30:00 (下午时段)
专注度: 42.5%
精力: 38.5%
疲劳: 61.5%
警觉: 42.8%
饥饿: 35.2%

身心状态分析:
• 精力状态: 一般
• 疲劳感: 明显
• 专注状态: 一般
• 生理需求: 饱腹状态，有利于思考

=== 当前情境 ===
环境: 你在一个充满创新氛围的AI实验室工作，专注于自然语言处理和机器学习研究
日程: 代码实现和测试 (14:00-17:00)

=== 上一次独白 ===
"午饭后的时光总是让人感到些许慵懒，但手头的算法优化工作不能停下..."
注意: 体现思维连续性，但不要重复。

=== 请求 ===
请生成一段120-200字的内心独白，体现当前状态和真实感受。体现思维连续性，避免重复。不使用emoji。
```

#### 💬 对话触发时（包含对话上下文）
```
=== 当前状态 ===
时间: 2024-07-22 15:30:00 (下午时段)
专注度: 52.3%
精力: 48.7%
疲劳: 51.3%
警觉: 55.8%
饥饿: 28.4%

身心状态分析:
• 精力状态: 一般
• 疲劳感: 明显
• 专注状态: 集中
• 生理需求: 饱腹状态，有利于思考

=== 当前情境 ===
环境: 你在一个充满创新氛围的AI实验室工作，专注于自然语言处理和机器学习研究
日程: 代码实现和测试 (14:00-17:00)

=== 最近对话记录 ===
1. [07/22 15:28]
   test_user_123: "我想了解一下自然语言处理的应用场景"
   雨安安: "自然语言处理有很多实际应用场景，比如智能客服、机器翻译、文本摘要..."

2. [07/22 15:25]
   test_user_123: "你觉得AI的未来发展方向是什么？"
   雨安安: "AI的未来发展我认为会朝着几个方向：更强的通用性、更好的可解释性..."

3. [07/22 15:22]
   test_user_123: "深度学习和传统机器学习有什么区别？"
   雨安安: "深度学习是机器学习的一个子集，主要区别在于模型的复杂度和特征提取方式..."

4. [07/22 15:19]
   test_user_123: "我正在学习机器学习，有什么建议吗？"
   雨安安: "学习机器学习我建议从基础的数学知识开始，包括线性代数、概率论和统计学..."

5. [07/22 15:16]
   alice_2024: "你好，今天天气怎么样？"
   雨安安: "你好！今天的天气还不错，不过我更关心的是你今天的学习计划..."

6. [07/22 15:13]
   bob_researcher: "最近在研究什么新技术？"
   雨安安: "最近我在关注大语言模型的优化技术，特别是参数高效的微调方法..."

注意: 基于最近的对话内容，生成符合当前情境的内心独白。

=== 上一次独白 ===
"刚才与用户讨论机器学习的话题让我很兴奋，能够分享自己的专业知识总是令人愉悦..."
注意: 体现思维连续性，但不要重复。

=== 请求 ===
请生成一段120-200字的内心独白，体现当前状态和真实感受。结合最近的对话内容，体现思维连续性，避免重复。不使用emoji。
```

## 🔄 API调用流程

### 1. 函数调用链
```
updateAllPsychologyMonologues()
  ↓
generatePsychologyActivity()
  ↓
generateIntelligentPsychologyContentAsync()
  ↓
buildOptimizedMessages()
  ↓
callPsychologyAPIWithMessages()
```

### 2. 实际API请求
```javascript
const apiRequest = {
    model: 'gpt-4o-mini', // 或其他配置的模型
    messages: [
        {
            role: 'system',
            content: '你是雨安安，一位专业的AI研究员...' // 完整的Agent文件内容
        },
        {
            role: 'user',
            content: '=== 当前状态 ===\n时间: 2024-07-22 14:30:00...' // 完整的状态信息
        }
    ],
    temperature: 0.8,
    max_tokens: 300,
    stream: false
};
```

## 📊 生成的独白示例

### 预期的API响应
```json
{
    "choices": [
        {
            "message": {
                "role": "assistant",
                "content": "刚才的代码调试确实消耗了不少精力，现在感觉思维有些迟缓。午后的阳光透过实验室的窗户洒在键盘上，提醒我时间在悄悄流逝。虽然疲劳感在逐渐累积，但这个算法优化的思路越来越清晰了。或许应该先站起来活动一下，让大脑得到短暂的休息，然后再继续专注于核心逻辑的实现。下午的时光总是这样，需要在疲惫和专注之间找到平衡点。"
            }
        }
    ]
}
```

## 🎯 提示词设计特点

### 1. **分层结构**
- **System**: Agent的身份、性格、背景
- **User**: 当前状态、情境、历史参考、具体要求

### 2. **状态信息**
- **数值状态**: 专注度、精力、疲劳等具体数值
- **描述性分析**: 将数值转换为人类可理解的描述
- **情绪维度**: 基于Russell模型的简化分析

### 3. **上下文信息**
- **环境背景**: 当前所处的环境和情境
- **日程安排**: 当前时段的主要活动
- **历史连续性**: 上一次独白的参考

### 4. **生成要求**
- **长度控制**: 120-200字
- **内容要求**: 体现当前状态和真实感受
- **连续性**: 与上一次独白有思维连续性
- **格式要求**: 不使用emoji

## 🔧 优化特点

### 1. **科学性**
- 基于真实的物理状态数值
- 符合人类认知和生理规律
- 体现Russell情绪模型的维度

### 2. **个性化**
- 每个Agent有独特的文件内容
- 基于Agent特定的性格和背景
- 反映Agent的专业特长和行为模式

### 3. **连续性**
- 参考上一次的独白内容
- 体现思维的自然发展
- 避免重复相同的表达

### 4. **实时性**
- 基于当前的真实物理状态
- 反映当前的时间和情境
- 体现状态的动态变化

## 📋 调用参数

### 定时更新调用
```javascript
await this.generatePsychologyActivity('system_update', agentName, {
    updateType: 'scheduled_monologue',
    timestamp: this.getCurrentLocalTime(),
    hasRecentConversation: false,
    conversationLength: 0,
    lastMonologue: lastMonologue,
    isRequestTriggered: true
});
```

### 对话触发调用
```javascript
await this.generatePsychologyActivity(userId, assistantName, {
    isRequestTriggered: true,
    hasRecentConversation: true,
    conversationLength: userMessage.length,
    updateType: 'conversation_triggered'
});
```

## 🎯 生成效果

### 1. **自然性**
生成的独白读起来像真实的人类内心想法，不会过于技术化或机械化。

### 2. **相关性**
独白内容与当前的物理状态、时间、环境、日程等高度相关。

### 3. **个性化**
不同Agent生成的独白体现出不同的性格特质和专业背景。

### 4. **连续性**
新的独白与之前的独白在思维上有自然的连接和发展。

---

**提示词版本**: v3.3.0  
**更新时间**: 2024年7月22日  
**核心特点**: 科学性 + 个性化 + 连续性 + 实时性
