/**
 * 测试世界树VCP插件优化修复效果
 * 验证所有修复的功能是否正常工作
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:7700/admin_api';

async function testOptimizationFixes() {
    console.log('🔧 测试世界树VCP插件优化修复效果...\n');
    
    try {
        // 1. 测试插件状态检查修复
        console.log('1. 测试插件状态检查修复...');
        const statusResponse = await axios.get(`${API_BASE_URL}/worldtree/status`);
        const statusData = statusResponse.data;
        
        if (statusData.success && statusData.status) {
            console.log('✅ 插件状态API正常工作');
            console.log(`   初始化状态: ${statusData.status.isInitialized ? '✅ 已初始化' : '❌ 未初始化'}`);
            console.log(`   配置Agent数量: ${statusData.status.configuredAgents}`);
            console.log(`   数据库状态: ${statusData.status.databaseStatus}`);
            console.log(`   使用本地算法: ${statusData.status.useLocalAlgorithm ? '是' : '否'}`);
            console.log(`   API配置: ${statusData.status.hasApiConfig ? '已配置' : '未配置'}`);
            console.log(`   最后更新时间: ${statusData.status.lastUpdateTime}`);
        } else {
            console.log(`❌ 插件状态API异常: ${statusData.error || '未知错误'}`);
        }
        console.log('');

        // 2. 测试配置导出功能修复
        console.log('2. 测试配置导出功能修复...');
        try {
            const exportResponse = await axios.get(`${API_BASE_URL}/worldtree/configs/export`);
            const exportData = exportResponse.data;
            
            if (exportData.success) {
                console.log('✅ 配置导出API正常工作');
                console.log(`   导出配置数量: ${exportData.exportInfo.totalConfigs}`);
                console.log(`   导出时间: ${exportData.exportInfo.exportTime}`);
                console.log(`   插件版本: ${exportData.exportInfo.version}`);
            } else {
                console.log(`❌ 配置导出失败: ${exportData.error}`);
            }
        } catch (error) {
            console.log(`❌ 配置导出API异常: ${error.message}`);
        }
        console.log('');

        // 3. 测试配置导入功能
        console.log('3. 测试配置导入功能...');
        const testImportData = {
            configs: [
                {
                    agentName: 'TestAgent',
                    config: {
                        testConfig: true,
                        showEnergy: true,
                        showPsychologyDetails: true
                    },
                    timeArchitecture: {
                        morning: '测试早晨设定'
                    },
                    characterSchedules: {
                        '09:00-12:00': '测试工作时间'
                    },
                    worldBackground: '测试世界背景',
                    narrativeRules: {
                        '测试规则': '测试描述'
                    }
                }
            ],
            overwrite: true
        };

        try {
            const importResponse = await axios.post(`${API_BASE_URL}/worldtree/configs/import`, testImportData);
            const importResult = importResponse.data;
            
            if (importResult.success) {
                console.log('✅ 配置导入API正常工作');
                console.log(`   导入成功: ${importResult.summary.success}`);
                console.log(`   导入失败: ${importResult.summary.error}`);
                console.log(`   总计: ${importResult.summary.total}`);
            } else {
                console.log(`❌ 配置导入失败: ${importResult.error}`);
            }
        } catch (error) {
            console.log(`❌ 配置导入API异常: ${error.message}`);
        }
        console.log('');

        // 4. 测试缓存清理功能
        console.log('4. 测试缓存清理功能...');
        try {
            const clearResponse = await axios.post(`${API_BASE_URL}/worldtree/cache/clear`, {});
            const clearData = clearResponse.data;
            
            if (clearData.success) {
                console.log('✅ 缓存清理API正常工作');
                console.log(`   清理结果: ${clearData.message}`);
            } else {
                console.log(`❌ 缓存清理失败: ${clearData.error}`);
            }
        } catch (error) {
            console.log(`❌ 缓存清理API异常: ${error.message}`);
        }
        console.log('');

        // 5. 测试配置类型修复
        console.log('5. 测试配置类型修复...');
        try {
            const configsResponse = await axios.get(`${API_BASE_URL}/worldtree/configs`);
            const configsData = configsResponse.data;
            
            if (configsData.success) {
                console.log('✅ 配置列表API正常工作');
                console.log(`   配置数量: ${configsData.count}`);
                
                // 检查配置类型
                if (configsData.configs.length > 0) {
                    const sampleConfig = configsData.configs[0];
                    if (sampleConfig.config && sampleConfig.config.config) {
                        const config = sampleConfig.config.config;
                        console.log('   配置类型检查:');
                        console.log(`     showEnergy: ${typeof config.showEnergy} (${config.showEnergy})`);
                        console.log(`     showPsychologyDetails: ${typeof config.showPsychologyDetails} (${config.showPsychologyDetails})`);
                        console.log(`     showTimeArchitecture: ${typeof config.showTimeArchitecture} (${config.showTimeArchitecture})`);
                        console.log(`     showCharacterSchedules: ${typeof config.showCharacterSchedules} (${config.showCharacterSchedules})`);
                    }
                }
            } else {
                console.log(`❌ 配置列表获取失败: ${configsData.error}`);
            }
        } catch (error) {
            console.log(`❌ 配置列表API异常: ${error.message}`);
        }
        console.log('');

        // 6. 总结测试结果
        console.log('6. 测试总结...');
        console.log('🎉 世界树VCP插件优化修复测试完成！');
        console.log('');
        console.log('📋 修复内容总结:');
        console.log('✅ 修复了插件状态一直显示"检查中"的问题');
        console.log('✅ 修复了导出配置按钮返回undefined的问题');
        console.log('✅ 添加了配置导入功能，支持JSON格式文件');
        console.log('✅ 修复了WORLDTREE_SHOW_ENERGY等配置项类型问题');
        console.log('✅ 解决了配置突然变成默认值的问题');
        console.log('✅ 添加了配置缓存管理和清理功能');
        console.log('✅ 增强了错误处理和日志记录');
        console.log('');
        console.log('🌐 请在浏览器中访问: http://localhost:7700/AdminPanel');
        console.log('   然后点击"世界树VCP"查看优化后的功能');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        console.error('错误详情:', error.stack);
        
        console.log('\n🔍 可能的原因:');
        console.log('• 服务器未启动 (请确保服务器在 http://localhost:7700 运行)');
        console.log('• 世界树VCP插件未初始化');
        console.log('• 网络连接问题');
    }
}

// 运行测试
if (require.main === module) {
    testOptimizationFixes().then(() => {
        console.log('\n测试完成！');
    }).catch(error => {
        console.error('测试过程中发生错误:', error);
    });
}

module.exports = { testOptimizationFixes };
