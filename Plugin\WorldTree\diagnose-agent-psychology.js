/**
 * 诊断Agent心理活动生成问题
 * 检查为什么只有一个Agent生效而另一个不生效
 */

const path = require('path');
const fs = require('fs').promises;

async function diagnoseAgentPsychology() {
    console.log('🔍 诊断Agent心理活动生成问题...\n');
    
    let worldTreeVCP = null;
    
    try {
        // 1. 初始化世界树VCP插件
        console.log('1. 初始化世界树VCP插件...');
        const WorldTreeVCP = require('./WorldTreeVCP.js');
        worldTreeVCP = new WorldTreeVCP();
        
        const mockLogger = {
            info: (tag, ...args) => console.log(`[INFO] ${tag}:`, ...args),
            warning: (tag, ...args) => console.log(`[WARN] ${tag}:`, ...args),
            error: (tag, ...args) => console.log(`[ERROR] ${tag}:`, ...args),
            debug: (tag, ...args) => console.log(`[DEBUG] ${tag}:`, ...args)
        };
        
        const initResult = await worldTreeVCP.initialize(mockLogger);
        if (!initResult) {
            throw new Error('插件初始化失败');
        }
        console.log('✅ 插件初始化成功\n');
        
        // 2. 检查Agent文件
        console.log('2. 检查Agent文件...');
        const agentDir = path.join(__dirname, '../../Agent');
        let agentFiles = [];
        
        try {
            const files = await fs.readdir(agentDir);
            agentFiles = files.filter(file => path.extname(file).toLowerCase() === '.txt');
            console.log(`发现 ${agentFiles.length} 个Agent文件:`);
            agentFiles.forEach(file => {
                const agentName = path.basename(file, '.txt');
                console.log(`  - ${agentName} (${file})`);
            });
        } catch (error) {
            console.log(`❌ 无法读取Agent目录: ${error.message}`);
            return;
        }
        console.log('');
        
        // 3. 检查Agent列表和配置状态
        console.log('3. 检查Agent配置状态...');
        const agents = await worldTreeVCP.getAgentList();
        console.log(`getAgentList() 返回 ${agents.length} 个Agent:`);
        
        for (const agent of agents) {
            console.log(`\n📋 Agent: ${agent.name}`);
            console.log(`   文件: ${agent.filename}`);
            console.log(`   内存配置: ${agent.hasWorldTreeConfig ? '✅' : '❌'}`);
            
            // 检查数据库配置
            const dbConfig = await worldTreeVCP.getWorldTreeConfig(agent.name);
            console.log(`   数据库配置: ${dbConfig ? '✅' : '❌'}`);
            
            if (dbConfig) {
                console.log(`   配置详情:`);
                console.log(`     - 时间架构: ${dbConfig.timeArchitecture ? '✅' : '❌'}`);
                console.log(`     - 角色日程: ${dbConfig.characterSchedules ? '✅' : '❌'}`);
                console.log(`     - 世界背景: ${dbConfig.worldBackground ? '✅' : '❌'}`);
                console.log(`     - 叙事规则: ${dbConfig.narrativeRules ? '✅' : '❌'}`);
                console.log(`     - 创建时间: ${dbConfig.created_time}`);
                console.log(`     - 更新时间: ${dbConfig.updated_time}`);
            }
        }
        console.log('');
        
        // 4. 检查数据库中的配置记录
        console.log('4. 检查数据库配置记录...');
        try {
            const dbConfigs = await worldTreeVCP.dbAll(`
                SELECT agent_name, created_time, updated_time 
                FROM world_tree_configs 
                ORDER BY updated_time DESC
            `);
            
            console.log(`数据库中有 ${dbConfigs.length} 个Agent配置:`);
            dbConfigs.forEach(config => {
                console.log(`  - ${config.agent_name} (创建: ${config.created_time}, 更新: ${config.updated_time})`);
            });
        } catch (error) {
            console.log(`❌ 数据库查询失败: ${error.message}`);
        }
        console.log('');
        
        // 5. 测试心理活动生成
        console.log('5. 测试每个Agent的心理活动生成...');
        for (const agent of agents) {
            console.log(`\n🧠 测试Agent: ${agent.name}`);
            
            try {
                // 测试心理活动生成
                const result = await worldTreeVCP.generatePsychologyActivity('test_user', agent.name, {
                    hasRecentConversation: true,
                    conversationLength: 100,
                    updateType: 'manual_test',
                    isRequestTriggered: true
                });
                
                if (result) {
                    console.log(`✅ 心理活动生成成功`);
                    console.log(`   内容: "${result.content?.substring(0, 100)}..."`);
                    console.log(`   时间: ${result.timestamp}`);
                    console.log(`   来源: ${result.source}`);
                    console.log(`   专注度: ${result.psychologyState?.focus?.toFixed(1)}%`);
                    console.log(`   精力: ${result.psychologyState?.energy?.toFixed(1)}%`);
                } else {
                    console.log(`❌ 心理活动生成失败 - 返回空结果`);
                }
            } catch (error) {
                console.log(`❌ 心理活动生成异常: ${error.message}`);
            }
        }
        console.log('');
        
        // 6. 检查心理活动日志记录
        console.log('6. 检查心理活动日志记录...');
        try {
            const logs = await worldTreeVCP.dbAll(`
                SELECT agent_name, COUNT(*) as count, MAX(created_time) as latest
                FROM psychology_monologue 
                GROUP BY agent_name 
                ORDER BY count DESC
            `);
            
            console.log(`心理活动日志统计:`);
            if (logs.length === 0) {
                console.log('  ❌ 没有找到任何心理活动日志记录');
            } else {
                logs.forEach(log => {
                    console.log(`  - ${log.agent_name}: ${log.count} 条记录 (最新: ${log.latest})`);
                });
            }
        } catch (error) {
            console.log(`❌ 心理活动日志查询失败: ${error.message}`);
        }
        console.log('');
        
        // 7. 检查定时更新状态
        console.log('7. 检查定时更新状态...');
        console.log(`定时器状态: ${worldTreeVCP.psychologyTimer ? '✅ 运行中' : '❌ 未启动'}`);
        console.log(`更新间隔: ${worldTreeVCP.config.psychologyUpdateInterval / 1000} 秒`);
        console.log(`上次自动更新: ${worldTreeVCP.lastAutoUpdateTime ? new Date(worldTreeVCP.lastAutoUpdateTime).toLocaleString() : '从未'}`);
        console.log(`上次请求更新: ${worldTreeVCP.lastRequestTime ? new Date(worldTreeVCP.lastRequestTime).toLocaleString() : '从未'}`);
        console.log('');
        
        // 8. 诊断结论
        console.log('8. 诊断结论...');
        const configuredAgents = agents.filter(agent => agent.hasWorldTreeConfig);
        const workingAgents = [];
        
        for (const agent of agents) {
            try {
                const result = await worldTreeVCP.generatePsychologyActivity('test_user', agent.name, {
                    isRequestTriggered: true
                });
                if (result) {
                    workingAgents.push(agent.name);
                }
            } catch (error) {
                // 忽略错误，只统计成功的
            }
        }
        
        console.log('📊 诊断总结:');
        console.log(`  总Agent数量: ${agents.length}`);
        console.log(`  已配置Agent: ${configuredAgents.length}`);
        console.log(`  正常工作Agent: ${workingAgents.length}`);
        console.log(`  工作的Agent: ${workingAgents.join(', ')}`);
        
        if (workingAgents.length < agents.length) {
            console.log('\n🔧 修复建议:');
            const notWorkingAgents = agents.filter(agent => !workingAgents.includes(agent.name));
            notWorkingAgents.forEach(agent => {
                console.log(`  - ${agent.name}: 需要在管理界面配置世界树VCP`);
            });
            console.log('\n📝 配置步骤:');
            console.log('  1. 访问管理界面: http://localhost:7700/AdminPanel');
            console.log('  2. 进入"世界树VCP"页面');
            console.log('  3. 为每个Agent创建配置');
            console.log('  4. 保存配置后等待心理活动生成');
        } else {
            console.log('\n✅ 所有Agent都正常工作！');
        }
        
    } catch (error) {
        console.error('❌ 诊断过程中发生错误:', error.message);
        console.error('错误堆栈:', error.stack);
    } finally {
        // 清理资源
        if (worldTreeVCP && worldTreeVCP.psychologyTimer) {
            clearInterval(worldTreeVCP.psychologyTimer);
        }
        if (worldTreeVCP && worldTreeVCP.db) {
            worldTreeVCP.db.close();
        }
    }
}

// 运行诊断
if (require.main === module) {
    diagnoseAgentPsychology().then(() => {
        console.log('\n🎯 诊断完成！');
        process.exit(0);
    }).catch(error => {
        console.error('诊断失败:', error);
        process.exit(1);
    });
}

module.exports = { diagnoseAgentPsychology };
