/**
 * 修复双Agent心理活动问题
 * 解决只有一个Agent生效的问题
 */

const path = require('path');
const fs = require('fs').promises;

async function fixDualAgentIssue() {
    console.log('🔧 修复双Agent心理活动问题...\n');
    
    let worldTreeVCP = null;
    
    try {
        // 1. 初始化世界树VCP插件
        console.log('1. 初始化世界树VCP插件...');
        const WorldTreeVCP = require('./WorldTreeVCP.js');
        worldTreeVCP = new WorldTreeVCP();
        
        const mockLogger = {
            info: (tag, ...args) => console.log(`[INFO] ${tag}:`, ...args),
            warning: (tag, ...args) => console.log(`[WARN] ${tag}:`, ...args),
            error: (tag, ...args) => console.log(`[ERROR] ${tag}:`, ...args),
            debug: (tag, ...args) => console.log(`[DEBUG] ${tag}:`, ...args)
        };
        
        const initResult = await worldTreeVCP.initialize(mockLogger);
        if (!initResult) {
            throw new Error('插件初始化失败');
        }
        console.log('✅ 插件初始化成功\n');
        
        // 2. 检查配置的Agent
        console.log('2. 检查配置的Agent...');
        const dbConfigs = await worldTreeVCP.dbAll(`
            SELECT agent_name FROM world_tree_configs ORDER BY updated_time DESC
        `);
        
        if (dbConfigs.length === 0) {
            console.log('❌ 没有找到任何世界树配置！');
            console.log('请先在管理界面为Agent创建世界树配置。');
            return;
        }
        
        console.log(`发现 ${dbConfigs.length} 个配置的Agent:`);
        dbConfigs.forEach((config, index) => {
            console.log(`  ${index + 1}. ${config.agent_name}`);
        });
        console.log('');
        
        // 3. 修复问题1：清理可能的缓存问题
        console.log('3. 清理缓存问题...');
        for (const config of dbConfigs) {
            const agentName = config.agent_name;
            
            // 清除内存缓存
            if (worldTreeVCP.worldTreeConfigs.has(agentName)) {
                worldTreeVCP.worldTreeConfigs.delete(agentName);
                worldTreeVCP.configCacheTimestamps.delete(agentName);
                console.log(`✅ 清除 ${agentName} 的内存缓存`);
            }
            
            // 重新加载配置
            const reloadedConfig = await worldTreeVCP.getWorldTreeConfig(agentName);
            if (reloadedConfig) {
                console.log(`✅ 重新加载 ${agentName} 的配置`);
            } else {
                console.log(`❌ ${agentName} 配置加载失败`);
            }
        }
        console.log('');
        
        // 4. 修复问题2：检查Agent文件存在性
        console.log('4. 检查Agent文件存在性...');
        const agentDir = path.join(__dirname, '../../Agent');
        const missingAgentFiles = [];
        
        for (const config of dbConfigs) {
            const agentName = config.agent_name;
            const agentFilePath = path.join(agentDir, `${agentName}.txt`);
            
            try {
                await fs.access(agentFilePath);
                console.log(`✅ ${agentName}.txt 文件存在`);
            } catch (error) {
                console.log(`❌ ${agentName}.txt 文件不存在`);
                missingAgentFiles.push(agentName);
                
                // 创建默认Agent文件
                const defaultContent = `你是${agentName}，一位AI助手。

## 角色设定
- 名称：${agentName}
- 性格：友善、乐于助人
- 专长：对话交流、问题解答

## 行为准则
1. 保持友善和耐心
2. 提供准确有用的信息
3. 尊重用户的需求和感受

请根据当前的心理状态和情境，生成真实的内心独白。`;
                
                try {
                    await fs.writeFile(agentFilePath, defaultContent, 'utf-8');
                    console.log(`✅ 为 ${agentName} 创建了默认Agent文件`);
                } catch (writeError) {
                    console.log(`❌ 创建 ${agentName} Agent文件失败: ${writeError.message}`);
                }
            }
        }
        console.log('');
        
        // 5. 修复问题3：重置定时更新机制
        console.log('5. 重置定时更新机制...');
        
        // 停止现有定时器
        if (worldTreeVCP.psychologyTimer) {
            clearInterval(worldTreeVCP.psychologyTimer);
            worldTreeVCP.psychologyTimer = null;
            console.log('✅ 停止现有定时器');
        }
        
        // 重置更新时间戳
        worldTreeVCP.lastAutoUpdateTime = 0;
        worldTreeVCP.lastRequestTime = 0;
        console.log('✅ 重置更新时间戳');
        
        // 重新启动定时器
        worldTreeVCP.startPsychologyUpdateTimer();
        console.log('✅ 重新启动定时器');
        console.log('');
        
        // 6. 修复问题4：强制为每个Agent生成心理活动
        console.log('6. 强制为每个Agent生成心理活动...');
        
        for (const config of dbConfigs) {
            const agentName = config.agent_name;
            console.log(`\n🧠 处理Agent: ${agentName}`);
            
            try {
                // 强制触发心理活动生成
                const result = await worldTreeVCP.generatePsychologyActivity('system_fix', agentName, {
                    hasRecentConversation: true,
                    conversationLength: 100,
                    updateType: 'force_fix',
                    isRequestTriggered: true
                });
                
                if (result) {
                    console.log(`✅ 心理活动生成成功`);
                    console.log(`   内容: "${result.content?.substring(0, 60)}..."`);
                    console.log(`   来源: ${result.source}`);
                    console.log(`   专注度: ${result.psychologyState?.focus?.toFixed(1)}%`);
                    console.log(`   精力: ${result.psychologyState?.energy?.toFixed(1)}%`);
                } else {
                    console.log(`❌ 心理活动生成失败 - 返回空结果`);
                    
                    // 尝试使用本地算法强制生成
                    console.log(`   尝试本地算法生成...`);
                    const localResult = await worldTreeVCP.calculatePsychologyState('system_fix', agentName, {
                        hasRecentConversation: true,
                        conversationLength: 100
                    });
                    
                    if (localResult) {
                        console.log(`✅ 本地算法生成成功`);
                        console.log(`   专注度: ${localResult.focus?.toFixed(1)}%`);
                        console.log(`   精力: ${localResult.energy?.toFixed(1)}%`);
                    }
                }
            } catch (error) {
                console.log(`❌ 处理 ${agentName} 时发生错误: ${error.message}`);
                
                // 检查是否是API相关错误
                if (error.message.includes('502') || error.message.includes('timeout') || error.message.includes('ECONNREFUSED')) {
                    console.log(`   这可能是API连接问题，尝试使用本地算法...`);
                    
                    try {
                        const localResult = await worldTreeVCP.calculatePsychologyState('system_fix', agentName, {
                            hasRecentConversation: true,
                            conversationLength: 100
                        });
                        
                        if (localResult) {
                            console.log(`✅ 本地算法作为备用方案成功`);
                        }
                    } catch (localError) {
                        console.log(`❌ 本地算法也失败: ${localError.message}`);
                    }
                }
            }
        }
        console.log('');
        
        // 7. 验证修复效果
        console.log('7. 验证修复效果...');
        
        // 等待一段时间让异步操作完成
        console.log('等待3秒让异步操作完成...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 检查心理活动日志
        try {
            const logs = await worldTreeVCP.dbAll(`
                SELECT agent_name, COUNT(*) as count, MAX(created_time) as latest
                FROM psychology_monologue 
                WHERE created_time > datetime('now', '-10 minutes')
                GROUP BY agent_name 
                ORDER BY count DESC
            `);
            
            console.log('📊 最近10分钟的心理活动记录:');
            if (logs.length === 0) {
                console.log('❌ 没有找到最近的心理活动记录');
            } else {
                logs.forEach(log => {
                    console.log(`  - ${log.agent_name}: ${log.count} 条记录 (最新: ${log.latest})`);
                });
                
                const activeAgents = logs.map(log => log.agent_name);
                const inactiveAgents = dbConfigs.filter(config => !activeAgents.includes(config.agent_name));
                
                if (inactiveAgents.length === 0) {
                    console.log('✅ 所有Agent都有心理活动记录！');
                } else {
                    console.log(`⚠️ 以下Agent没有心理活动记录: ${inactiveAgents.map(a => a.agent_name).join(', ')}`);
                }
            }
        } catch (error) {
            console.log(`❌ 验证查询失败: ${error.message}`);
        }
        console.log('');
        
        // 8. 提供后续建议
        console.log('8. 后续建议...');
        console.log('✅ 修复操作已完成！');
        console.log('');
        console.log('📋 后续步骤:');
        console.log('1. 重启服务器以确保所有修复生效');
        console.log('2. 在管理界面检查心理状态监控页面');
        console.log('3. 观察两个Agent是否都有心理活动更新');
        console.log('4. 如果仍有问题，检查服务器日志获取详细错误信息');
        console.log('');
        console.log('🔍 可能的原因:');
        console.log('- API配置问题导致某些Agent调用失败');
        console.log('- Agent文件缺失或格式错误');
        console.log('- 缓存问题导致配置未正确加载');
        console.log('- 定时更新机制异常');
        console.log('');
        console.log('💡 如果问题持续存在:');
        console.log('- 检查API Key是否有效且有足够余额');
        console.log('- 确认两个Agent的世界树配置都完整');
        console.log('- 查看服务器控制台的详细日志输出');
        
    } catch (error) {
        console.error('❌ 修复过程中发生错误:', error.message);
        console.error('错误堆栈:', error.stack);
    } finally {
        // 清理资源
        if (worldTreeVCP && worldTreeVCP.psychologyTimer) {
            clearInterval(worldTreeVCP.psychologyTimer);
        }
        if (worldTreeVCP && worldTreeVCP.db) {
            worldTreeVCP.db.close();
        }
    }
}

// 运行修复
if (require.main === module) {
    fixDualAgentIssue().then(() => {
        console.log('\n🎯 修复完成！');
        process.exit(0);
    }).catch(error => {
        console.error('修复失败:', error);
        process.exit(1);
    });
}

module.exports = { fixDualAgentIssue };
