{"name": "WorldTreeVCP", "displayName": "世界树VCP插件", "version": "1.0.0", "description": "世界树VCP插件 - 时间架构与角色心理活动系统", "author": "VCPToolBox", "main": "WorldTreeVCP.js", "type": "vcp", "category": "psychology", "enabled": true, "dependencies": {"sqlite3": "^5.1.7", "fs": "builtin", "path": "builtin"}, "configSchema": {"WORLDTREE_ENABLED": {"type": "boolean", "default": true, "description": "启用世界树VCP插件", "required": false}, "WORLDTREE_USE_LOCAL_ALGORITHM": {"type": "boolean", "default": true, "description": "优先使用本地算法生成心理状态", "required": false}, "WORLDTREE_PSYCHOLOGY_UPDATE_INTERVAL": {"type": "integer", "default": 300000, "description": "心理状态更新间隔（毫秒）", "required": false, "minimum": 60000, "maximum": 3600000}, "WORLDTREE_MAX_CACHE_AGE": {"type": "integer", "default": 1800000, "description": "缓存最大存活时间（毫秒）", "required": false, "minimum": 300000, "maximum": 7200000}, "WORLDTREE_API_URL": {"type": "string", "default": "", "description": "API服务URL（可选）", "required": false}, "WORLDTREE_API_KEY": {"type": "string", "default": "", "description": "API密钥（可选）", "required": false}, "WORLDTREE_MODEL": {"type": "string", "default": "gpt-4o-mini", "description": "使用的AI模型", "required": false, "enum": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "claude-3-sonnet", "claude-3-haiku"]}, "WORLDTREE_TIMEOUT": {"type": "integer", "default": 120000, "description": "API请求超时时间（毫秒）", "required": false, "minimum": 30000, "maximum": 600000}, "WORLDTREE_STRESS_WEIGHT": {"type": "number", "default": 0.3, "description": "压力权重系数", "required": false, "minimum": 0.0, "maximum": 1.0}, "WORLDTREE_EMOTION_WEIGHT": {"type": "number", "default": 0.25, "description": "情绪权重系数", "required": false, "minimum": 0.0, "maximum": 1.0}, "WORLDTREE_ENERGY_WEIGHT": {"type": "number", "default": 0.2, "description": "能量权重系数", "required": false, "minimum": 0.0, "maximum": 1.0}, "WORLDTREE_MOOD_WEIGHT": {"type": "number", "default": 0.15, "description": "心情权重系数", "required": false, "minimum": 0.0, "maximum": 1.0}, "WORLDTREE_FOCUS_WEIGHT": {"type": "number", "default": 0.1, "description": "专注度权重系数", "required": false, "minimum": 0.0, "maximum": 1.0}, "WORLDTREE_TIME_FORMAT": {"type": "string", "default": "local", "description": "时间格式", "required": false, "enum": ["local", "utc", "iso"]}, "WORLDTREE_DEBUG_MODE": {"type": "boolean", "default": false, "description": "启用调试模式", "required": false}, "WORLDTREE_LOG_LEVEL": {"type": "string", "default": "info", "description": "日志级别", "required": false, "enum": ["debug", "info", "warn", "error"]}, "WORLDTREE_SHOW_ENERGY": {"type": "boolean", "default": true, "description": "是否显示精力水平", "required": false}, "WORLDTREE_SHOW_PSYCHOLOGY_DETAILS": {"type": "boolean", "default": true, "description": "是否显示心理状态详情", "required": false}, "WORLDTREE_SHOW_TIME_ARCHITECTURE": {"type": "boolean", "default": true, "description": "是否显示时间架构信息", "required": false}, "WORLDTREE_SHOW_CHARACTER_SCHEDULES": {"type": "boolean", "default": true, "description": "是否显示角色日程表", "required": false}}, "features": ["时间架构管理", "角色日程表配置", "心理状态算法计算", "心理活动内容生成", "系统消息注入", "本地算法优先", "数据库集成"], "database": {"tables": ["world_tree_configs", "psychology_activities", "time_events"], "integrates_with": "AdvancedMemorySystem"}, "api_endpoints": ["/admin_api/worldtree/configs", "/admin_api/worldtree/agents", "/admin_api/worldtree/psychology", "/admin_api/worldtree/status"], "admin_panel": {"has_interface": true, "integrated": true, "sections": ["世界树配置", "Agent管理", "心理状态监控", "时间架构设置"]}, "permissions": ["database_read", "database_write", "agent_access", "system_message_injection"], "tags": ["psychology", "worldtree", "character", "narrative", "algorithm", "local"]}