/**
 * 测试简化后的对话上下文功能
 * 验证：使用数据库实际字段、显示真实的用户ID和AI名称
 */

const axios = require('axios');

async function testSimplifiedContext() {
    console.log('🔧 测试简化后的对话上下文功能...\n');
    
    try {
        // 1. 发送测试对话
        console.log('1. 发送测试对话建立上下文...');
        const testUserId = 'test_user_123';
        const testAgent = '雨安安';
        
        const conversations = [
            '你好，我是小明，今天想学习AI知识',
            '能介绍一下机器学习的基本概念吗？',
            '深度学习和机器学习有什么区别？'
        ];
        
        console.log(`使用用户ID: ${testUserId}`);
        console.log(`目标Agent: ${testAgent}`);
        console.log(`发送 ${conversations.length} 轮对话...`);
        
        for (let i = 0; i < conversations.length; i++) {
            try {
                console.log(`  ${i + 1}. 发送: "${conversations[i]}"`);
                
                const response = await axios.post('http://localhost:7700/v1/chat/completions', {
                    model: 'gpt-4o-mini',
                    messages: [
                        { role: 'user', content: conversations[i] }
                    ],
                    assistantName: testAgent,
                    userId: testUserId,
                    stream: false
                });
                
                if (response.status === 200) {
                    const aiResponse = response.data.choices[0].message.content;
                    console.log(`     回复: "${aiResponse.substring(0, 50)}..."`);
                } else {
                    console.log(`     ❌ 对话 ${i + 1} 失败`);
                }
                
                // 等待记忆系统处理
                await new Promise(resolve => setTimeout(resolve, 1500));
                
            } catch (error) {
                console.log(`     ⚠️ 对话 ${i + 1} 异常: ${error.message}`);
            }
        }
        console.log('');
        
        // 2. 等待心理状态更新
        console.log('2. 等待心理状态更新...');
        console.log('等待8秒让异步心理状态更新完成...');
        await new Promise(resolve => setTimeout(resolve, 8000));
        
        // 3. 检查最新的心理活动记录
        console.log('3. 检查心理活动记录...');
        try {
            const logsResponse = await axios.get('http://localhost:7700/admin_api/worldtree/psychology/activities', {
                params: { limit: 5 }
            });
            
            if (logsResponse.data.success) {
                const logs = logsResponse.data.data;
                const recentLogs = logs.filter(log => log.agentName === testAgent);
                
                console.log(`找到 ${recentLogs.length} 条 ${testAgent} 的心理活动记录:`);
                
                recentLogs.forEach((log, index) => {
                    console.log(`\n${index + 1}. [${formatTime(log.created_time)}] ${log.generation_method}`);
                    console.log(`   内容: "${log.content?.substring(0, 120)}..."`);
                    
                    // 检查是否包含对话相关内容
                    const content = log.content || '';
                    const hasUserRef = content.includes(testUserId) || content.includes('小明');
                    const hasConversationRef = content.includes('对话') || content.includes('交流') || 
                                             content.includes('学习') || content.includes('机器学习');
                    
                    if (hasUserRef) {
                        console.log(`   ✅ 独白提到了用户`);
                    }
                    if (hasConversationRef) {
                        console.log(`   ✅ 独白体现了对话内容`);
                    }
                    if (!hasUserRef && !hasConversationRef) {
                        console.log(`   ⚠️ 独白未明显体现对话内容`);
                    }
                });
            } else {
                console.log('❌ 无法获取心理活动记录');
            }
        } catch (error) {
            console.log(`⚠️ 心理活动记录检查失败: ${error.message}`);
        }
        console.log('');
        
        // 4. 展示预期的User消息结构
        console.log('4. 预期的User消息结构（简化版）...');
        console.log('当主程序触发心理状态更新时，User消息应该包含:');
        console.log('');
        console.log('=== 当前状态 ===');
        console.log('时间: 2024-07-22 15:30:00 (下午时段)');
        console.log('专注度: 52.3%');
        console.log('精力: 48.7%');
        console.log('疲劳: 51.3%');
        console.log('警觉: 55.8%');
        console.log('饥饿: 28.4%');
        console.log('');
        console.log('身心状态分析:');
        console.log('• 精力状态: 一般');
        console.log('• 疲劳感: 明显');
        console.log('• 专注状态: 集中');
        console.log('• 生理需求: 饱腹状态，有利于思考');
        console.log('');
        console.log('=== 当前情境 ===');
        console.log('环境: AI实验室工作环境');
        console.log('日程: 代码实现和测试 (14:00-17:00)');
        console.log('');
        console.log('=== 最近对话记录 ===');
        console.log('1. [07/22 15:28]');
        console.log(`   ${testUserId}: "深度学习和机器学习有什么区别？"`);
        console.log(`   ${testAgent}: "深度学习是机器学习的一个子集，主要区别在于..."`);
        console.log('');
        console.log('2. [07/22 15:25]');
        console.log(`   ${testUserId}: "能介绍一下机器学习的基本概念吗？"`);
        console.log(`   ${testAgent}: "机器学习是人工智能的一个重要分支..."`);
        console.log('');
        console.log('3. [07/22 15:22]');
        console.log(`   ${testUserId}: "你好，我是小明，今天想学习AI知识"`);
        console.log(`   ${testAgent}: "你好小明！很高兴你对AI感兴趣..."`);
        console.log('');
        console.log('注意: 基于最近的对话内容，生成符合当前情境的内心独白。');
        console.log('');
        console.log('=== 上一次独白 ===');
        console.log('"刚才与用户讨论机器学习的话题让我很兴奋..."');
        console.log('注意: 体现思维连续性，但不要重复。');
        console.log('');
        console.log('=== 请求 ===');
        console.log('请生成一段120-200字的内心独白，体现当前状态和真实感受。结合最近的对话内容，体现思维连续性，避免重复。不使用emoji。');
        console.log('');
        
        // 5. 关键改进点
        console.log('5. 简化后的关键改进...');
        
        const improvements = [
            '直接使用数据库中的 user_id 字段作为用户名称',
            '直接使用数据库中的 persona_name 字段作为AI名称',
            '删除了复杂的用户名称提取逻辑',
            '删除了硬编码的名称匹配模式',
            '保持了对话上下文的完整性和可读性',
            '简化了代码逻辑，提高了可维护性'
        ];
        
        console.log('✅ 关键改进点:');
        improvements.forEach((improvement, index) => {
            console.log(`  ${index + 1}. ${improvement}`);
        });
        
        console.log('\n🎯 数据库字段映射:');
        console.log('• user_id → 对话记录中的用户名称');
        console.log('• persona_name → 对话记录中的AI名称');
        console.log('• user_message → 用户的消息内容');
        console.log('• ai_response → AI的回复内容');
        console.log('• creation_time → 对话时间戳');
        
        console.log('\n📋 验证要点:');
        console.log('1. 对话记录显示真实的用户ID（如 test_user_123）');
        console.log('2. 对话记录显示真实的AI名称（如 雨安安）');
        console.log('3. 不再尝试从消息中提取用户名称');
        console.log('4. 保持简洁清晰的对话上下文格式');
        console.log('5. 独白内容能够体现最近的对话主题');
        
        console.log('\n🎊 预期效果:');
        console.log('- 对话上下文显示准确的用户ID和AI名称');
        console.log('- 代码逻辑更简洁，无复杂的名称提取');
        console.log('- 独白内容基于真实的对话历史生成');
        console.log('- 系统更稳定，减少了名称提取的错误');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        if (error.code === 'ECONNREFUSED') {
            console.log('请确保服务器在 http://localhost:7700 运行');
        }
    }
}

// 辅助函数
function formatTime(timestamp) {
    if (!timestamp) return '--';
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 运行测试
if (require.main === module) {
    testSimplifiedContext().then(() => {
        console.log('\n🎯 简化对话上下文功能测试完成！');
        console.log('\n💡 下一步：');
        console.log('1. 重启服务器应用更新');
        console.log('2. 使用不同的用户ID进行对话测试');
        console.log('3. 观察心理活动记录中的用户名称显示');
        console.log('4. 验证对话上下文的准确性');
    }).catch(error => {
        console.error('测试失败:', error);
    });
}

module.exports = { testSimplifiedContext };
