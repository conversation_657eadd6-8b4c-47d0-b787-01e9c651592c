/**
 * 调试世界树VCP插件导出问题
 * 检查API端点和数据流
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:7700/admin_api';

async function debugExportIssue() {
    console.log('🔍 调试世界树VCP插件导出问题...\n');
    
    try {
        // 1. 检查服务器是否运行
        console.log('1. 检查服务器连接...');
        try {
            const healthResponse = await axios.get('http://localhost:7700');
            console.log('✅ 服务器连接正常');
        } catch (error) {
            console.log('❌ 服务器连接失败:', error.message);
            console.log('请确保服务器在 http://localhost:7700 运行');
            return;
        }
        console.log('');

        // 2. 检查世界树插件状态
        console.log('2. 检查世界树插件状态...');
        try {
            const statusResponse = await axios.get(`${API_BASE_URL}/worldtree/status`);
            const statusData = statusResponse.data;
            
            console.log('状态API响应:', JSON.stringify(statusData, null, 2));
            
            if (statusData.success && statusData.status.isInitialized) {
                console.log('✅ 世界树插件已初始化');
                console.log(`   配置Agent数量: ${statusData.status.configuredAgents}`);
            } else {
                console.log('❌ 世界树插件未初始化或有问题');
                console.log('   错误:', statusData.status?.error || statusData.error);
                return;
            }
        } catch (error) {
            console.log('❌ 状态API调用失败:', error.message);
            if (error.response) {
                console.log('   HTTP状态:', error.response.status);
                console.log('   响应数据:', error.response.data);
            }
            return;
        }
        console.log('');

        // 3. 检查Agent列表
        console.log('3. 检查Agent列表...');
        try {
            const configsResponse = await axios.get(`${API_BASE_URL}/worldtree/configs`);
            const configsData = configsResponse.data;
            
            console.log('配置列表API响应:', JSON.stringify(configsData, null, 2));
            
            if (configsData.success) {
                console.log('✅ 配置列表获取成功');
                console.log(`   Agent数量: ${configsData.count}`);
                
                if (configsData.configs && configsData.configs.length > 0) {
                    console.log('   Agent详情:');
                    configsData.configs.forEach(config => {
                        console.log(`     - ${config.agentName}: ${config.hasConfig ? '已配置' : '未配置'}`);
                    });
                } else {
                    console.log('   ⚠️ 没有找到任何Agent配置');
                }
            } else {
                console.log('❌ 配置列表获取失败:', configsData.error);
            }
        } catch (error) {
            console.log('❌ 配置列表API调用失败:', error.message);
            if (error.response) {
                console.log('   HTTP状态:', error.response.status);
                console.log('   响应数据:', error.response.data);
            }
        }
        console.log('');

        // 4. 测试导出API
        console.log('4. 测试导出API...');
        try {
            const exportResponse = await axios.get(`${API_BASE_URL}/worldtree/configs/export`);
            const exportData = exportResponse.data;
            
            console.log('导出API完整响应:');
            console.log(JSON.stringify(exportData, null, 2));
            
            if (exportData.success) {
                console.log('✅ 导出API调用成功');
                console.log(`   导出配置数量: ${exportData.configs?.length || 0}`);
                console.log(`   导出信息: ${JSON.stringify(exportData.exportInfo)}`);
                
                // 检查数据结构
                if (exportData.configs && Array.isArray(exportData.configs)) {
                    console.log('   ✅ configs字段格式正确');
                    
                    if (exportData.configs.length > 0) {
                        const sampleConfig = exportData.configs[0];
                        console.log('   示例配置结构:');
                        console.log(`     - agentName: ${sampleConfig.agentName}`);
                        console.log(`     - config存在: ${!!sampleConfig.config}`);
                        console.log(`     - exportTime: ${sampleConfig.exportTime}`);
                        
                        if (sampleConfig.config) {
                            console.log('     - config内容:');
                            console.log(`       * config: ${!!sampleConfig.config.config}`);
                            console.log(`       * timeArchitecture: ${!!sampleConfig.config.timeArchitecture}`);
                            console.log(`       * characterSchedules: ${!!sampleConfig.config.characterSchedules}`);
                            console.log(`       * worldBackground: ${!!sampleConfig.config.worldBackground}`);
                            console.log(`       * narrativeRules: ${!!sampleConfig.config.narrativeRules}`);
                        }
                    } else {
                        console.log('   ⚠️ 没有配置数据可导出');
                        console.log('   建议: 先创建一些Agent配置再测试导出功能');
                    }
                } else {
                    console.log('   ❌ configs字段格式错误');
                }
                
                if (exportData.exportInfo) {
                    console.log('   ✅ exportInfo字段存在');
                } else {
                    console.log('   ⚠️ exportInfo字段缺失');
                }
                
            } else {
                console.log('❌ 导出API返回失败:', exportData.error);
            }
        } catch (error) {
            console.log('❌ 导出API调用失败:', error.message);
            if (error.response) {
                console.log('   HTTP状态:', error.response.status);
                console.log('   响应数据:', JSON.stringify(error.response.data, null, 2));
            }
        }
        console.log('');

        // 5. 前端调试建议
        console.log('5. 前端调试建议...');
        console.log('如果导出功能在前端仍然返回undefined，请检查:');
        console.log('');
        console.log('📋 浏览器控制台调试步骤:');
        console.log('1. 打开浏览器开发者工具 (F12)');
        console.log('2. 切换到 Console 标签页');
        console.log('3. 点击"导出配置"按钮');
        console.log('4. 查看控制台输出的调试信息:');
        console.log('   - "开始导出世界树配置..."');
        console.log('   - "导出API响应状态: 200"');
        console.log('   - "导出API响应数据: {...}"');
        console.log('   - "准备下载的数据: {...}"');
        console.log('   - "配置导出完成"');
        console.log('');
        console.log('📋 网络标签页调试:');
        console.log('1. 切换到 Network 标签页');
        console.log('2. 点击"导出配置"按钮');
        console.log('3. 查看 /admin_api/worldtree/configs/export 请求:');
        console.log('   - 状态码应该是 200');
        console.log('   - 响应内容应该包含 configs 数组');
        console.log('');
        console.log('📋 常见问题排查:');
        console.log('• 如果看到 "正在导出配置..." 但没有下载文件:');
        console.log('  - 检查浏览器是否阻止了下载');
        console.log('  - 检查控制台是否有JavaScript错误');
        console.log('• 如果API返回空的configs数组:');
        console.log('  - 先创建一些Agent配置');
        console.log('  - 确保配置已正确保存');
        console.log('• 如果出现网络错误:');
        console.log('  - 检查服务器是否正常运行');
        console.log('  - 检查API端点是否正确');
        
    } catch (error) {
        console.error('❌ 调试过程中发生错误:', error.message);
        console.error('错误详情:', error.stack);
    }
}

// 运行调试
if (require.main === module) {
    debugExportIssue().then(() => {
        console.log('\n🔍 调试完成！');
        console.log('如果问题仍然存在，请按照上述建议在浏览器中进行进一步调试。');
    }).catch(error => {
        console.error('调试过程中发生错误:', error);
    });
}

module.exports = { debugExportIssue };
