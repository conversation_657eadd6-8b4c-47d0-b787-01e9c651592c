/**
 * 测试心理状态监控修复效果
 * 验证不同Agent数值差异、实时更新、详情切换等功能
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:7700/admin_api';

async function testPsychologyMonitorFixes() {
    console.log('🔧 测试心理状态监控修复效果...\n');
    
    try {
        // 1. 检查服务器连接
        console.log('1. 检查服务器连接...');
        try {
            await axios.get('http://localhost:7700');
            console.log('✅ 服务器连接正常');
        } catch (error) {
            console.log('❌ 服务器连接失败，请确保服务器在 http://localhost:7700 运行');
            return;
        }
        console.log('');

        // 2. 测试实时心理状态API
        console.log('2. 测试实时心理状态API...');
        const response = await axios.get(`${API_BASE_URL}/worldtree/psychology/realtime`);
        const data = response.data;
        
        if (data.success && data.data && data.data.length > 0) {
            console.log('✅ 实时心理状态API正常');
            console.log(`   获取到 ${data.data.length} 个Agent的数据`);
            
            // 检查数值差异
            console.log('\n📊 Agent数值差异检查:');
            data.data.forEach((agent, index) => {
                console.log(`   Agent ${index + 1}: ${agent.agentName}`);
                console.log(`     专注: ${agent.focus.toFixed(1)}, 精力: ${agent.energy.toFixed(1)}, 疲劳: ${agent.fatigue.toFixed(1)}`);
                console.log(`     警觉: ${agent.alertness.toFixed(1)}, 饥饿: ${agent.hunger.toFixed(1)}`);
            });
            
            // 检查是否有相同数值
            const focusValues = data.data.map(a => a.focus.toFixed(1));
            const energyValues = data.data.map(a => a.energy.toFixed(1));
            const uniqueFocus = [...new Set(focusValues)];
            const uniqueEnergy = [...new Set(energyValues)];
            
            if (uniqueFocus.length === focusValues.length && uniqueEnergy.length === energyValues.length) {
                console.log('✅ 所有Agent数值都不相同');
            } else {
                console.log('⚠️ 发现相同数值，需要进一步优化');
            }
        } else {
            console.log('❌ 实时心理状态API返回空数据');
            return;
        }
        console.log('');

        // 3. 测试多次调用的数值变化
        console.log('3. 测试数值实时变化...');
        const firstCall = await axios.get(`${API_BASE_URL}/worldtree/psychology/realtime`);
        
        // 等待2秒
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const secondCall = await axios.get(`${API_BASE_URL}/worldtree/psychology/realtime`);
        
        if (firstCall.data.success && secondCall.data.success) {
            const firstData = firstCall.data.data;
            const secondData = secondCall.data.data;
            
            let hasChanges = false;
            for (let i = 0; i < Math.min(firstData.length, secondData.length); i++) {
                const agent1 = firstData[i];
                const agent2 = secondData[i];
                
                if (agent1.agentName === agent2.agentName) {
                    const focusChange = Math.abs(agent1.focus - agent2.focus);
                    const energyChange = Math.abs(agent1.energy - agent2.energy);
                    
                    if (focusChange > 0.1 || energyChange > 0.1) {
                        hasChanges = true;
                        console.log(`   ${agent1.agentName}: 专注变化 ${focusChange.toFixed(1)}, 精力变化 ${energyChange.toFixed(1)}`);
                    }
                }
            }
            
            if (hasChanges) {
                console.log('✅ 数值正在实时变化');
            } else {
                console.log('⚠️ 数值没有明显变化，可能需要调整算法');
            }
        }
        console.log('');

        // 4. 测试特定Agent心理活动日志
        console.log('4. 测试特定Agent心理活动日志...');
        if (data.data && data.data.length > 0) {
            const testAgent = data.data[0].agentName;
            console.log(`   测试Agent: ${testAgent}`);
            
            try {
                const logsResponse = await axios.get(`${API_BASE_URL}/worldtree/psychology/logs?agentName=${encodeURIComponent(testAgent)}&limit=10`);
                const logsData = logsResponse.data;
                
                if (logsData.success) {
                    console.log(`✅ 成功获取 ${testAgent} 的心理活动日志`);
                    console.log(`   日志数量: ${logsData.logs ? logsData.logs.length : 0}`);
                    
                    if (logsData.logs && logsData.logs.length > 0) {
                        const latestLog = logsData.logs[0];
                        console.log(`   最新记录: "${latestLog.monologue_content?.substring(0, 50)}..."`);
                        console.log(`   记录时间: ${latestLog.created_time}`);
                    }
                } else {
                    console.log(`❌ 获取 ${testAgent} 心理活动日志失败:`, logsData.error);
                }
            } catch (error) {
                console.log(`❌ 请求 ${testAgent} 心理活动日志异常:`, error.message);
            }
        }
        console.log('');

        // 5. 测试所有Agent心理活动日志
        console.log('5. 测试所有Agent心理活动日志...');
        try {
            const allLogsResponse = await axios.get(`${API_BASE_URL}/worldtree/psychology/logs?limit=20`);
            const allLogsData = allLogsResponse.data;
            
            if (allLogsData.success) {
                console.log('✅ 成功获取所有Agent心理活动日志');
                console.log(`   总日志数量: ${allLogsData.logs ? allLogsData.logs.length : 0}`);
                
                if (allLogsData.logs && allLogsData.logs.length > 0) {
                    // 统计不同Agent的日志数量
                    const agentCounts = {};
                    allLogsData.logs.forEach(log => {
                        const agent = log.agent_name || 'unknown';
                        agentCounts[agent] = (agentCounts[agent] || 0) + 1;
                    });
                    
                    console.log('   各Agent日志分布:');
                    Object.entries(agentCounts).forEach(([agent, count]) => {
                        console.log(`     ${agent}: ${count} 条`);
                    });
                }
            } else {
                console.log('❌ 获取所有Agent心理活动日志失败:', allLogsData.error);
            }
        } catch (error) {
            console.log('❌ 请求所有Agent心理活动日志异常:', error.message);
        }
        console.log('');

        // 6. 总结修复效果
        console.log('6. 修复效果总结...');
        console.log('🎉 心理状态监控修复测试完成！');
        console.log('');
        console.log('📋 修复内容:');
        console.log('✅ 后端为每个Agent生成不同的心理状态数值');
        console.log('✅ 添加了实时变化因子和随机性');
        console.log('✅ 前端支持Agent详情查看和切换');
        console.log('✅ 支持特定Agent心理活动日志查看');
        console.log('✅ 添加了返回全部Agent视图功能');
        console.log('');
        console.log('🌐 使用方法:');
        console.log('1. 在管理界面的"心理状态监控"标签页查看实时数据');
        console.log('2. 点击Agent行的"详情"按钮查看单个Agent');
        console.log('3. 在详情视图中点击"返回全部"回到总览');
        console.log('4. 数值会每5秒自动更新，每个Agent都有不同的变化模式');
        console.log('');
        console.log('⚠️ 注意事项:');
        console.log('- 数值变化基于时间函数和随机因子');
        console.log('- 每个Agent有独特的变化模式');
        console.log('- 心理活动日志支持按Agent筛选');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        if (error.response) {
            console.error('HTTP状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 运行测试
if (require.main === module) {
    testPsychologyMonitorFixes().then(() => {
        console.log('\n测试完成！');
    }).catch(error => {
        console.error('测试过程中发生错误:', error);
    });
}

module.exports = { testPsychologyMonitorFixes };
