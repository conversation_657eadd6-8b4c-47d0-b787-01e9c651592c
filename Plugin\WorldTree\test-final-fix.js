/**
 * 测试最终修复效果
 * 验证：2秒延迟、每次对话都触发、详细日志追踪
 */

const axios = require('axios');

async function testFinalFix() {
    console.log('🔧 测试最终修复效果...\n');
    
    try {
        // 1. 准备测试
        console.log('1. 准备测试环境...');
        const testUserId = 'test_final_fix';
        const testAgent = '雨安安';
        
        console.log(`测试用户: ${testUserId}`);
        console.log(`测试Agent: ${testAgent}`);
        console.log('');
        
        // 2. 连续快速对话测试
        console.log('2. 连续快速对话测试（验证每次都触发）...');
        const conversations = [
            '第一条测试消息',
            '第二条测试消息',
            '第三条测试消息'
        ];
        
        const results = [];
        
        for (let i = 0; i < conversations.length; i++) {
            const startTime = Date.now();
            console.log(`\n📤 发送第 ${i + 1} 条消息: "${conversations[i]}"`);
            
            try {
                const response = await axios.post('http://localhost:7700/v1/chat/completions', {
                    model: 'gpt-4o-mini',
                    messages: [
                        { role: 'user', content: conversations[i] }
                    ],
                    assistantName: testAgent,
                    userId: testUserId,
                    stream: false
                });
                
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                if (response.status === 200) {
                    const aiResponse = response.data.choices[0].message.content;
                    console.log(`✅ 对话成功 (${responseTime}ms): "${aiResponse.substring(0, 50)}..."`);
                    console.log(`⏰ 心理独白将在2秒后开始生成...`);
                    
                    results.push({
                        index: i + 1,
                        success: true,
                        responseTime,
                        timestamp: new Date().toISOString()
                    });
                } else {
                    console.log(`❌ 对话失败: ${response.status}`);
                    results.push({
                        index: i + 1,
                        success: false,
                        responseTime,
                        error: `HTTP ${response.status}`
                    });
                }
                
                // 等待3秒，让2秒延迟的心理独白处理完成
                console.log('⏳ 等待4秒让延迟的心理独白处理完成...');
                await new Promise(resolve => setTimeout(resolve, 4000));
                
            } catch (error) {
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                console.log(`❌ 对话异常 (${responseTime}ms): ${error.message}`);
                
                results.push({
                    index: i + 1,
                    success: false,
                    responseTime,
                    error: error.message
                });
            }
        }
        
        console.log('\n📊 对话结果统计:');
        const successCount = results.filter(r => r.success).length;
        console.log(`成功: ${successCount}/${results.length}`);
        console.log(`平均响应时间: ${Math.round(results.reduce((sum, r) => sum + r.responseTime, 0) / results.length)}ms`);
        
        // 3. 等待所有异步处理完成
        console.log('\n3. 等待所有异步处理完成...');
        console.log('等待5秒确保所有心理独白都已生成...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // 4. 检查心理活动记录
        console.log('4. 检查心理活动记录...');
        try {
            const logsResponse = await axios.get('http://localhost:7700/admin_api/worldtree/psychology/activities', {
                params: { limit: 10 }
            });
            
            if (logsResponse.data.success) {
                const logs = logsResponse.data.data;
                const testLogs = logs.filter(log => 
                    log.agentName === testAgent && 
                    new Date(log.created_time) > new Date(Date.now() - 3 * 60 * 1000) // 最近3分钟
                );
                
                console.log(`\n📋 找到 ${testLogs.length} 条最近的心理活动记录:`);
                
                testLogs.forEach((log, index) => {
                    console.log(`\n${index + 1}. [${formatTime(log.created_time)}] ${log.generation_method}`);
                    console.log(`   内容: "${log.content?.substring(0, 80)}..."`);
                    
                    // 检查是否包含测试相关内容
                    const content = log.content || '';
                    const hasTestRef = content.includes('测试') || content.includes('消息');
                    
                    if (hasTestRef) {
                        console.log(`   ✅ 独白体现了测试对话内容`);
                    } else {
                        console.log(`   ⚠️ 独白未明显体现测试内容`);
                    }
                });
                
                // 验证触发频率
                console.log(`\n🎯 触发验证:`);
                console.log(`发送对话: ${conversations.length} 条`);
                console.log(`生成独白: ${testLogs.length} 条`);
                
                if (testLogs.length >= conversations.length) {
                    console.log(`✅ 触发频率正常 (${testLogs.length}/${conversations.length})`);
                } else if (testLogs.length >= conversations.length * 0.8) {
                    console.log(`⚠️ 触发频率偏低但可接受 (${testLogs.length}/${conversations.length})`);
                } else {
                    console.log(`❌ 触发频率过低 (${testLogs.length}/${conversations.length})`);
                }
                
            } else {
                console.log('❌ 无法获取心理活动记录');
            }
        } catch (error) {
            console.log(`⚠️ 心理活动记录检查失败: ${error.message}`);
        }
        
        // 5. 验证修复效果
        console.log('\n5. 最终修复效果验证...');
        
        const finalFixes = [
            '✅ server.js中添加了2秒延迟，为数据库预留时间',
            '✅ 对话触发完全独立于自动更新时间限制',
            '✅ 每次调用都有唯一ID追踪，便于调试',
            '✅ 异步生成函数有详细的日志记录',
            '✅ 保存过程有完整的状态追踪'
        ];
        
        console.log('🎊 最终修复内容:');
        finalFixes.forEach((fix, index) => {
            console.log(`  ${index + 1}. ${fix}`);
        });
        
        console.log('\n🔍 服务器日志检查要点:');
        console.log('现在应该看到这样的日志序列:');
        console.log('1. "对话触发：将在2秒后异步更新Agent [Agent名称] 的心理状态"');
        console.log('2. "开始执行延迟的心理状态更新: [Agent名称]"');
        console.log('3. "🎯 心理分析调用开始 [CallID] [Agent名称]"');
        console.log('4. "✅ 对话触发，立即执行: [Agent名称]"');
        console.log('5. "🎯 开始异步API调用 [CallID]: [Agent名称]"');
        console.log('6. "🚀 开始异步生成心理独白 [AsyncID]: [Agent名称]"');
        console.log('7. "💾 开始保存心理独白 [AsyncID]: [Agent名称]"');
        console.log('8. "✅ 异步心理独白生成并保存完成 [AsyncID]: [Agent名称]"');
        console.log('9. "✅ 异步API生成完成 [CallID] [Agent名称]，耗时: X.XX秒"');
        console.log('10. "🎊 对话触发的心理独白生成成功 [CallID]: [Agent名称]"');
        
        console.log('\n📋 问题解决验证:');
        console.log('□ 每次对话都触发心理独白（无"只能触发一次"问题）');
        console.log('□ 2秒延迟确保数据库信息完整');
        console.log('□ 详细日志追踪每个调用过程');
        console.log('□ 对话触发独立于自动更新');
        console.log('□ 异步处理不影响对话响应速度');
        
        console.log('\n🎯 预期效果:');
        console.log('- 用户连续发送消息，每次都会在2秒后触发心理独白');
        console.log('- 不再出现"第一次可以，后面不行"的问题');
        console.log('- 服务器日志显示完整的处理链路');
        console.log('- 心理活动记录正确保存并显示');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        if (error.code === 'ECONNREFUSED') {
            console.log('请确保服务器在 http://localhost:7700 运行');
        }
    }
}

// 辅助函数
function formatTime(timestamp) {
    if (!timestamp) return '--';
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 运行测试
if (require.main === module) {
    testFinalFix().then(() => {
        console.log('\n🎯 最终修复测试完成！');
        console.log('\n💡 下一步：');
        console.log('1. 重启服务器应用所有修复');
        console.log('2. 进行连续对话测试');
        console.log('3. 观察服务器日志的详细输出');
        console.log('4. 验证每次对话都在2秒后触发心理独白');
        console.log('5. 确认心理活动记录正确生成和保存');
    }).catch(error => {
        console.error('测试失败:', error);
    });
}

module.exports = { testFinalFix };
