/**
 * 测试世界树VCP插件导出导入功能
 * 验证配置文件格式和API功能
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const API_BASE_URL = 'http://localhost:7700/admin_api';

async function testExportImport() {
    console.log('🔧 测试世界树VCP插件导出导入功能...\n');
    
    try {
        // 1. 测试导出功能
        console.log('1. 测试配置导出功能...');
        try {
            const exportResponse = await axios.get(`${API_BASE_URL}/worldtree/configs/export`);
            const exportData = exportResponse.data;
            
            console.log('导出API响应:', JSON.stringify(exportData, null, 2));
            
            if (exportData.success) {
                console.log('✅ 配置导出API正常工作');
                console.log(`   导出配置数量: ${exportData.exportInfo?.totalConfigs || exportData.configs?.length || 0}`);
                console.log(`   导出时间: ${exportData.exportInfo?.exportTime || '未知'}`);
                
                // 保存导出的配置到文件
                const exportFilePath = path.join(__dirname, 'exported-configs.json');
                fs.writeFileSync(exportFilePath, JSON.stringify(exportData, null, 2));
                console.log(`   配置已保存到: ${exportFilePath}`);
                
                // 验证导出文件格式
                console.log('\n   验证导出文件格式:');
                if (exportData.configs && Array.isArray(exportData.configs)) {
                    console.log('   ✅ configs字段存在且为数组');
                    console.log(`   ✅ 包含 ${exportData.configs.length} 个配置项`);
                    
                    if (exportData.configs.length > 0) {
                        const sampleConfig = exportData.configs[0];
                        console.log('   ✅ 示例配置结构:');
                        console.log(`      - agentName: ${sampleConfig.agentName || '缺失'}`);
                        console.log(`      - config: ${sampleConfig.config ? '存在' : '缺失'}`);
                        console.log(`      - exportTime: ${sampleConfig.exportTime || '缺失'}`);
                    }
                } else {
                    console.log('   ❌ configs字段缺失或格式错误');
                }
                
                if (exportData.exportInfo) {
                    console.log('   ✅ exportInfo字段存在');
                } else {
                    console.log('   ⚠️ exportInfo字段缺失');
                }
                
            } else {
                console.log(`❌ 配置导出失败: ${exportData.error}`);
            }
        } catch (error) {
            console.log(`❌ 配置导出API异常: ${error.message}`);
            if (error.response) {
                console.log(`   HTTP状态: ${error.response.status}`);
                console.log(`   响应数据:`, error.response.data);
            }
        }
        console.log('');

        // 2. 测试导入功能
        console.log('2. 测试配置导入功能...');
        
        // 读取示例配置文件
        const exampleConfigPath = path.join(__dirname, 'example-import-config.json');
        if (fs.existsSync(exampleConfigPath)) {
            try {
                const exampleConfig = JSON.parse(fs.readFileSync(exampleConfigPath, 'utf8'));
                
                console.log('   读取示例配置文件成功');
                console.log(`   配置数量: ${exampleConfig.configs.length}`);
                
                // 测试导入API
                const importResponse = await axios.post(`${API_BASE_URL}/worldtree/configs/import`, {
                    configs: exampleConfig.configs,
                    overwrite: true
                });
                
                const importResult = importResponse.data;
                console.log('导入API响应:', JSON.stringify(importResult, null, 2));
                
                if (importResult.success) {
                    console.log('✅ 配置导入API正常工作');
                    console.log(`   导入成功: ${importResult.summary.success}`);
                    console.log(`   导入失败: ${importResult.summary.error}`);
                    console.log(`   总计: ${importResult.summary.total}`);
                    
                    if (importResult.results) {
                        console.log('   详细结果:');
                        importResult.results.forEach(result => {
                            const status = result.success ? '✅' : '❌';
                            console.log(`     ${status} ${result.agentName}: ${result.message || result.error}`);
                        });
                    }
                } else {
                    console.log(`❌ 配置导入失败: ${importResult.error}`);
                }
                
            } catch (error) {
                console.log(`❌ 配置导入API异常: ${error.message}`);
                if (error.response) {
                    console.log(`   HTTP状态: ${error.response.status}`);
                    console.log(`   响应数据:`, error.response.data);
                }
            }
        } else {
            console.log('   ❌ 示例配置文件不存在');
        }
        console.log('');

        // 3. 验证导入后的配置
        console.log('3. 验证导入后的配置...');
        try {
            const configsResponse = await axios.get(`${API_BASE_URL}/worldtree/configs`);
            const configsData = configsResponse.data;
            
            if (configsData.success) {
                console.log('✅ 配置列表获取成功');
                console.log(`   当前配置数量: ${configsData.count}`);
                
                if (configsData.configs.length > 0) {
                    console.log('   配置详情:');
                    configsData.configs.forEach(config => {
                        console.log(`     - ${config.agentName}: ${config.hasConfig ? '已配置' : '未配置'}`);
                    });
                }
            } else {
                console.log(`❌ 配置列表获取失败: ${configsData.error}`);
            }
        } catch (error) {
            console.log(`❌ 配置列表API异常: ${error.message}`);
        }
        console.log('');

        // 4. 总结
        console.log('4. 测试总结...');
        console.log('🎉 导出导入功能测试完成！');
        console.log('');
        console.log('📋 文件格式说明:');
        console.log('导出的配置文件格式为JSON，包含以下结构:');
        console.log('{');
        console.log('  "success": true,');
        console.log('  "configs": [');
        console.log('    {');
        console.log('      "agentName": "Agent名称",');
        console.log('      "config": {');
        console.log('        "config": { 显示配置项 },');
        console.log('        "timeArchitecture": { 时间架构 },');
        console.log('        "characterSchedules": { 角色日程 },');
        console.log('        "worldBackground": "世界背景",');
        console.log('        "narrativeRules": { 叙事规则 }');
        console.log('      },');
        console.log('      "exportTime": "导出时间"');
        console.log('    }');
        console.log('  ],');
        console.log('  "exportInfo": {');
        console.log('    "totalConfigs": 配置数量,');
        console.log('    "exportTime": "导出时间",');
        console.log('    "version": "版本号"');
        console.log('  }');
        console.log('}');
        console.log('');
        console.log('📁 示例文件:');
        console.log('- example-import-config.json - 导入示例文件');
        console.log('- exported-configs.json - 导出的配置文件');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        console.error('错误详情:', error.stack);
        
        console.log('\n🔍 可能的原因:');
        console.log('• 服务器未启动 (请确保服务器在 http://localhost:7700 运行)');
        console.log('• 世界树VCP插件未初始化');
        console.log('• 网络连接问题');
    }
}

// 运行测试
if (require.main === module) {
    testExportImport().then(() => {
        console.log('\n测试完成！');
    }).catch(error => {
        console.error('测试过程中发生错误:', error);
    });
}

module.exports = { testExportImport };
