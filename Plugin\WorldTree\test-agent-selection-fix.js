/**
 * 测试Agent选择逻辑修复
 * 验证刷新时Agent选择不会混乱
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:7700/admin_api';

async function testAgentSelectionFix() {
    console.log('🔧 测试Agent选择逻辑修复...\n');
    
    try {
        // 1. 检查服务器连接
        console.log('1. 检查服务器连接...');
        try {
            const response = await axios.get(`${API_BASE_URL}/worldtree/psychology/realtime`, {
                timeout: 5000
            });
            
            if (response.data.success) {
                console.log('✅ 服务器连接正常');
                console.log(`发现 ${response.data.data.length} 个Agent`);
            } else {
                console.log('❌ 服务器响应错误:', response.data.error);
                return;
            }
        } catch (error) {
            console.log('❌ 服务器连接失败，请确保服务器在 http://localhost:7700 运行');
            return;
        }
        console.log('');
        
        // 2. 测试Agent数据顺序稳定性
        console.log('2. 测试Agent数据顺序稳定性...');
        const requests = [];
        for (let i = 0; i < 5; i++) {
            requests.push(axios.get(`${API_BASE_URL}/worldtree/psychology/realtime`));
        }
        
        const responses = await Promise.all(requests);
        const agentOrders = responses.map(res => res.data.data.map(agent => agent.agentName));
        
        console.log('多次请求的Agent顺序:');
        agentOrders.forEach((order, index) => {
            console.log(`  请求 ${index + 1}: [${order.join(', ')}]`);
        });
        
        // 检查顺序是否一致
        const firstOrder = JSON.stringify(agentOrders[0]);
        const isOrderConsistent = agentOrders.every(order => JSON.stringify(order) === firstOrder);
        
        if (isOrderConsistent) {
            console.log('✅ Agent顺序稳定，每次请求返回相同顺序');
        } else {
            console.log('❌ Agent顺序不稳定，可能导致选择混乱');
        }
        console.log('');
        
        // 3. 测试Agent数据差异性
        console.log('3. 测试Agent数据差异性...');
        const latestData = responses[0].data.data;
        
        if (latestData.length >= 2) {
            console.log('Agent数据对比:');
            latestData.forEach(agent => {
                console.log(`  ${agent.agentName}:`);
                console.log(`    专注: ${agent.focus?.toFixed(1)}%`);
                console.log(`    精力: ${agent.energy?.toFixed(1)}%`);
                console.log(`    疲劳: ${agent.fatigue?.toFixed(1)}%`);
                console.log(`    警觉: ${agent.alertness?.toFixed(1)}%`);
            });
            
            // 检查数值是否有差异
            const agent1 = latestData[0];
            const agent2 = latestData[1];
            
            const hasDifference = 
                Math.abs(agent1.focus - agent2.focus) > 1 ||
                Math.abs(agent1.energy - agent2.energy) > 1 ||
                Math.abs(agent1.fatigue - agent2.fatigue) > 1 ||
                Math.abs(agent1.alertness - agent2.alertness) > 1;
            
            if (hasDifference) {
                console.log('✅ Agent数据有明显差异，算法工作正常');
            } else {
                console.log('⚠️ Agent数据过于相似，可能存在算法问题');
            }
        } else {
            console.log('⚠️ Agent数量不足，无法进行差异性测试');
        }
        console.log('');
        
        // 4. 模拟前端选择逻辑
        console.log('4. 模拟前端选择逻辑...');
        
        if (latestData.length >= 2) {
            const agent1Name = latestData[0].agentName;
            const agent2Name = latestData[1].agentName;
            
            console.log(`模拟选择Agent: ${agent1Name}`);
            
            // 模拟刷新操作
            console.log('模拟刷新操作...');
            const refreshResponse = await axios.get(`${API_BASE_URL}/worldtree/psychology/realtime`);
            const refreshedData = refreshResponse.data.data;
            
            // 检查Agent是否还在相同位置
            const agent1AfterRefresh = refreshedData.find(agent => agent.agentName === agent1Name);
            const agent2AfterRefresh = refreshedData.find(agent => agent.agentName === agent2Name);
            
            if (agent1AfterRefresh && agent2AfterRefresh) {
                console.log(`✅ 刷新后Agent ${agent1Name} 仍然存在`);
                console.log(`✅ 刷新后Agent ${agent2Name} 仍然存在`);
                
                // 检查数据是否有变化（应该有轻微变化）
                const focusChange = Math.abs(agent1AfterRefresh.focus - agent1.focus);
                if (focusChange > 0.1) {
                    console.log(`✅ Agent数据有实时变化 (专注度变化: ${focusChange.toFixed(1)})`);
                } else {
                    console.log(`⚠️ Agent数据变化很小，可能需要等待更长时间`);
                }
            } else {
                console.log('❌ 刷新后Agent数据丢失');
            }
        }
        console.log('');
        
        // 5. 测试心理活动日志
        console.log('5. 测试心理活动日志...');
        try {
            const logsResponse = await axios.get(`${API_BASE_URL}/worldtree/psychology/activities`, {
                params: { limit: 5 }
            });
            
            if (logsResponse.data.success) {
                const logs = logsResponse.data.data;
                console.log(`获取到 ${logs.length} 条心理活动日志`);
                
                if (logs.length > 0) {
                    const agentCounts = {};
                    logs.forEach(log => {
                        agentCounts[log.agentName] = (agentCounts[log.agentName] || 0) + 1;
                    });
                    
                    console.log('各Agent的日志数量:');
                    Object.entries(agentCounts).forEach(([agentName, count]) => {
                        console.log(`  ${agentName}: ${count} 条`);
                    });
                    
                    const uniqueAgents = Object.keys(agentCounts);
                    if (uniqueAgents.length >= 2) {
                        console.log('✅ 多个Agent都有心理活动记录');
                    } else {
                        console.log('⚠️ 只有一个Agent有心理活动记录，可能存在问题');
                    }
                } else {
                    console.log('⚠️ 没有心理活动日志记录');
                }
            } else {
                console.log('❌ 心理活动日志获取失败:', logsResponse.data.error);
            }
        } catch (error) {
            console.log('❌ 心理活动日志API调用失败:', error.message);
        }
        console.log('');
        
        // 6. 总结和建议
        console.log('6. 总结和建议...');
        console.log('✅ Agent选择逻辑修复测试完成');
        console.log('');
        console.log('🔧 修复内容:');
        console.log('1. 后端Agent列表按名称排序，确保顺序稳定');
        console.log('2. 前端刷新时保持当前选择的Agent');
        console.log('3. 添加调试日志帮助排查问题');
        console.log('4. 改进心理状态详情更新逻辑');
        console.log('');
        console.log('📋 使用建议:');
        console.log('1. 选择一个Agent查看详情后，点击刷新按钮');
        console.log('2. 确认右侧显示的Agent名称没有变化');
        console.log('3. 观察心理状态数值是否有实时更新');
        console.log('4. 如果仍有问题，查看浏览器控制台的调试日志');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
    }
}

// 运行测试
if (require.main === module) {
    testAgentSelectionFix().then(() => {
        console.log('\n🎯 测试完成！');
    }).catch(error => {
        console.error('测试失败:', error);
    });
}

module.exports = { testAgentSelectionFix };
