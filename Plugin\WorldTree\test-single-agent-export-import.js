/**
 * 测试世界树VCP插件单Agent导出导入功能
 * 验证基于当前选择Agent的导出导入逻辑
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const API_BASE_URL = 'http://localhost:7700/admin_api';

async function testSingleAgentExportImport() {
    console.log('🔧 测试世界树VCP插件单Agent导出导入功能...\n');
    
    try {
        // 1. 检查服务器连接
        console.log('1. 检查服务器连接...');
        try {
            await axios.get('http://localhost:7700');
            console.log('✅ 服务器连接正常');
        } catch (error) {
            console.log('❌ 服务器连接失败，请确保服务器在 http://localhost:7700 运行');
            return;
        }
        console.log('');

        // 2. 检查Agent列表
        console.log('2. 检查Agent列表...');
        const configsResponse = await axios.get(`${API_BASE_URL}/worldtree/configs`);
        const configsData = configsResponse.data;
        
        if (!configsData.success || !configsData.configs || configsData.configs.length === 0) {
            console.log('❌ 没有找到任何Agent配置');
            console.log('请先在管理界面创建一些Agent配置再测试导出导入功能');
            return;
        }
        
        console.log('✅ 找到Agent配置:');
        configsData.configs.forEach(config => {
            console.log(`   - ${config.agentName}: ${config.hasConfig ? '已配置' : '未配置'}`);
        });
        
        // 选择第一个有配置的Agent进行测试
        const testAgent = configsData.configs.find(config => config.hasConfig);
        if (!testAgent) {
            console.log('❌ 没有找到已配置的Agent');
            console.log('请先在管理界面为某个Agent创建世界树配置');
            return;
        }
        
        console.log(`✅ 选择测试Agent: ${testAgent.agentName}`);
        console.log('');

        // 3. 测试单Agent配置获取
        console.log('3. 测试单Agent配置获取...');
        const agentConfigResponse = await axios.get(`${API_BASE_URL}/worldtree/configs/${testAgent.agentName}`);
        const agentConfigData = agentConfigResponse.data;
        
        if (agentConfigData.success && agentConfigData.config) {
            console.log('✅ Agent配置获取成功');
            console.log(`   Agent: ${testAgent.agentName}`);
            console.log(`   配置结构:`);
            console.log(`     - config: ${!!agentConfigData.config.config}`);
            console.log(`     - timeArchitecture: ${!!agentConfigData.config.timeArchitecture}`);
            console.log(`     - characterSchedules: ${!!agentConfigData.config.characterSchedules}`);
            console.log(`     - worldBackground: ${!!agentConfigData.config.worldBackground}`);
            console.log(`     - narrativeRules: ${!!agentConfigData.config.narrativeRules}`);
        } else {
            console.log('❌ Agent配置获取失败:', agentConfigData.error);
            return;
        }
        console.log('');

        // 4. 模拟单Agent导出
        console.log('4. 模拟单Agent导出...');
        const exportData = {
            agentName: testAgent.agentName,
            config: agentConfigData.config,
            exportTime: new Date().toISOString(),
            version: "1.0.0"
        };
        
        // 保存导出文件
        const exportFilePath = path.join(__dirname, `exported-${testAgent.agentName}.json`);
        fs.writeFileSync(exportFilePath, JSON.stringify(exportData, null, 2));
        console.log('✅ 单Agent导出模拟成功');
        console.log(`   导出文件: ${exportFilePath}`);
        console.log(`   文件大小: ${fs.statSync(exportFilePath).size} 字节`);
        console.log('');

        // 5. 验证导出文件格式
        console.log('5. 验证导出文件格式...');
        const exportedData = JSON.parse(fs.readFileSync(exportFilePath, 'utf8'));
        
        const requiredFields = ['agentName', 'config', 'exportTime', 'version'];
        let formatValid = true;
        
        requiredFields.forEach(field => {
            if (exportedData[field] !== undefined) {
                console.log(`   ✅ ${field}: 存在`);
            } else {
                console.log(`   ❌ ${field}: 缺失`);
                formatValid = false;
            }
        });
        
        if (formatValid) {
            console.log('✅ 导出文件格式验证通过');
        } else {
            console.log('❌ 导出文件格式验证失败');
            return;
        }
        console.log('');

        // 6. 测试示例配置文件
        console.log('6. 测试示例配置文件...');
        const exampleConfigPath = path.join(__dirname, 'example-import-config.json');
        
        if (fs.existsSync(exampleConfigPath)) {
            const exampleConfig = JSON.parse(fs.readFileSync(exampleConfigPath, 'utf8'));
            console.log('✅ 示例配置文件读取成功');
            console.log(`   示例Agent: ${exampleConfig.agentName}`);
            console.log(`   配置版本: ${exampleConfig.version}`);
            console.log(`   导出时间: ${exampleConfig.exportTime}`);
            
            // 验证示例文件格式
            const exampleValid = requiredFields.every(field => exampleConfig[field] !== undefined);
            if (exampleValid) {
                console.log('✅ 示例配置文件格式正确');
            } else {
                console.log('❌ 示例配置文件格式错误');
            }
        } else {
            console.log('❌ 示例配置文件不存在');
        }
        console.log('');

        // 7. 总结
        console.log('7. 测试总结...');
        console.log('🎉 单Agent导出导入功能测试完成！');
        console.log('');
        console.log('📋 新功能特点:');
        console.log('✅ 基于当前选择的Agent进行导出');
        console.log('✅ 导出单个Agent的完整配置');
        console.log('✅ 导入时应用到当前选择的Agent');
        console.log('✅ 文件格式简化，更易理解');
        console.log('✅ 避免了空配置导出的问题');
        console.log('');
        console.log('📁 文件说明:');
        console.log(`- ${exportFilePath} - 实际导出的配置文件`);
        console.log('- example-import-config.json - 导入示例文件');
        console.log('');
        console.log('🌐 使用方法:');
        console.log('1. 在管理界面选择Agent并点击"编辑"');
        console.log('2. 点击"导出配置"下载当前Agent配置');
        console.log('3. 点击"导入配置"上传配置文件到当前Agent');
        console.log('');
        console.log('⚠️ 注意事项:');
        console.log('- 导出前必须先选择Agent');
        console.log('- 导入会覆盖当前Agent的配置');
        console.log('- 配置文件格式为单Agent结构');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        if (error.response) {
            console.error('HTTP状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 运行测试
if (require.main === module) {
    testSingleAgentExportImport().then(() => {
        console.log('\n测试完成！');
    }).catch(error => {
        console.error('测试过程中发生错误:', error);
    });
}

module.exports = { testSingleAgentExportImport };
