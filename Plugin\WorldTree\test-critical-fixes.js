/**
 * 测试关键问题修复效果
 * 验证：Agent差异性、数值动态变化、精力值合理性、专注度变化
 */

const axios = require('axios');

async function testCriticalFixes() {
    console.log('🔧 测试关键问题修复效果...\n');
    
    try {
        // 1. 测试Agent差异性
        console.log('1. 测试Agent差异性...');
        const response = await axios.get('http://localhost:7700/admin_api/worldtree/psychology/realtime');
        
        if (!response.data.success) {
            console.log('❌ API调用失败:', response.data.error);
            return;
        }
        
        const agents = response.data.data;
        console.log(`获取到 ${agents.length} 个Agent的数据:`);
        
        if (agents.length < 2) {
            console.log('⚠️ 只有一个Agent，无法测试差异性');
            console.log('请确保配置了多个Agent');
            return;
        }
        
        // 显示每个Agent的当前状态
        agents.forEach((agent, index) => {
            console.log(`\n${index + 1}. ${agent.agentName}:`);
            console.log(`   专注度: ${agent.focus.toFixed(2)}%`);
            console.log(`   精力: ${agent.energy.toFixed(2)}%`);
            console.log(`   疲劳: ${agent.fatigue.toFixed(2)}%`);
            console.log(`   警觉: ${agent.alertness.toFixed(2)}%`);
            console.log(`   饥饿: ${agent.hunger.toFixed(2)}%`);
        });
        
        // 计算Agent间的差异
        if (agents.length >= 2) {
            const agent1 = agents[0];
            const agent2 = agents[1];
            
            const focusDiff = Math.abs(agent1.focus - agent2.focus);
            const energyDiff = Math.abs(agent1.energy - agent2.energy);
            const fatigueDiff = Math.abs(agent1.fatigue - agent2.fatigue);
            const alertnessDiff = Math.abs(agent1.alertness - agent2.alertness);
            
            console.log('\n📊 Agent差异分析:');
            console.log(`专注度差异: ${focusDiff.toFixed(2)}%`);
            console.log(`精力差异: ${energyDiff.toFixed(2)}%`);
            console.log(`疲劳差异: ${fatigueDiff.toFixed(2)}%`);
            console.log(`警觉差异: ${alertnessDiff.toFixed(2)}%`);
            
            const avgDiff = (focusDiff + energyDiff + fatigueDiff + alertnessDiff) / 4;
            if (avgDiff > 5) {
                console.log(`✅ Agent差异性良好 (平均差异: ${avgDiff.toFixed(2)}%)`);
            } else {
                console.log(`❌ Agent差异性不足 (平均差异: ${avgDiff.toFixed(2)}%)`);
            }
        }
        console.log('');
        
        // 2. 测试数值合理性
        console.log('2. 测试数值合理性...');
        let hasUnreasonableValues = false;
        
        agents.forEach(agent => {
            const issues = [];
            
            if (agent.focus < 10 || agent.focus > 100) issues.push(`专注度异常: ${agent.focus.toFixed(2)}%`);
            if (agent.energy < 15 || agent.energy > 100) issues.push(`精力异常: ${agent.energy.toFixed(2)}%`);
            if (agent.fatigue < 5 || agent.fatigue > 95) issues.push(`疲劳异常: ${agent.fatigue.toFixed(2)}%`);
            if (agent.alertness < 20 || agent.alertness > 100) issues.push(`警觉异常: ${agent.alertness.toFixed(2)}%`);
            if (agent.hunger < 5 || agent.hunger > 95) issues.push(`饥饿异常: ${agent.hunger.toFixed(2)}%`);
            
            if (issues.length > 0) {
                console.log(`❌ ${agent.agentName} 数值异常:`);
                issues.forEach(issue => console.log(`   - ${issue}`));
                hasUnreasonableValues = true;
            } else {
                console.log(`✅ ${agent.agentName} 数值合理`);
            }
        });
        
        if (!hasUnreasonableValues) {
            console.log('✅ 所有Agent的数值都在合理范围内');
        }
        console.log('');
        
        // 3. 测试动态变化
        console.log('3. 测试动态变化（等待30秒）...');
        console.log('初始状态已记录，等待30秒观察变化...');
        
        await new Promise(resolve => setTimeout(resolve, 30000));
        
        const updatedResponse = await axios.get('http://localhost:7700/admin_api/worldtree/psychology/realtime');
        if (updatedResponse.data.success) {
            const updatedAgents = updatedResponse.data.data;
            
            console.log('📈 30秒后的变化分析:');
            agents.forEach(initialAgent => {
                const updatedAgent = updatedAgents.find(a => a.agentName === initialAgent.agentName);
                if (updatedAgent) {
                    const focusChange = Math.abs(updatedAgent.focus - initialAgent.focus);
                    const energyChange = Math.abs(updatedAgent.energy - initialAgent.energy);
                    const fatigueChange = Math.abs(updatedAgent.fatigue - initialAgent.fatigue);
                    const alertnessChange = Math.abs(updatedAgent.alertness - initialAgent.alertness);
                    
                    console.log(`\n${initialAgent.agentName}:`);
                    console.log(`  专注度: ${initialAgent.focus.toFixed(2)}% → ${updatedAgent.focus.toFixed(2)}% (变化: ${focusChange.toFixed(2)}%)`);
                    console.log(`  精力: ${initialAgent.energy.toFixed(2)}% → ${updatedAgent.energy.toFixed(2)}% (变化: ${energyChange.toFixed(2)}%)`);
                    console.log(`  疲劳: ${initialAgent.fatigue.toFixed(2)}% → ${updatedAgent.fatigue.toFixed(2)}% (变化: ${fatigueChange.toFixed(2)}%)`);
                    console.log(`  警觉: ${initialAgent.alertness.toFixed(2)}% → ${updatedAgent.alertness.toFixed(2)}% (变化: ${alertnessChange.toFixed(2)}%)`);
                    
                    const totalChange = focusChange + energyChange + fatigueChange + alertnessChange;
                    if (totalChange > 2) {
                        console.log(`  ✅ 有明显动态变化 (总变化: ${totalChange.toFixed(2)}%)`);
                    } else {
                        console.log(`  ❌ 变化不明显 (总变化: ${totalChange.toFixed(2)}%)`);
                    }
                }
            });
        }
        console.log('');
        
        // 4. 测试专注度是否不再固定在100%
        console.log('4. 测试专注度固定问题...');
        const focusValues = agents.map(agent => agent.focus);
        const maxFocus = Math.max(...focusValues);
        const minFocus = Math.min(...focusValues);
        const avgFocus = focusValues.reduce((sum, val) => sum + val, 0) / focusValues.length;
        
        console.log(`专注度范围: ${minFocus.toFixed(2)}% - ${maxFocus.toFixed(2)}%`);
        console.log(`平均专注度: ${avgFocus.toFixed(2)}%`);
        
        if (maxFocus >= 99 && minFocus >= 99) {
            console.log('❌ 专注度仍然固定在高值');
        } else if (maxFocus - minFocus < 1) {
            console.log('⚠️ 专注度变化范围太小');
        } else {
            console.log('✅ 专注度有合理的变化范围');
        }
        console.log('');
        
        // 5. 测试精力值是否不再异常低
        console.log('5. 测试精力值异常问题...');
        const energyValues = agents.map(agent => agent.energy);
        const maxEnergy = Math.max(...energyValues);
        const minEnergy = Math.min(...energyValues);
        const avgEnergy = energyValues.reduce((sum, val) => sum + val, 0) / energyValues.length;
        
        console.log(`精力范围: ${minEnergy.toFixed(2)}% - ${maxEnergy.toFixed(2)}%`);
        console.log(`平均精力: ${avgEnergy.toFixed(2)}%`);
        
        if (maxEnergy < 5) {
            console.log('❌ 精力值仍然异常低');
        } else if (minEnergy < 15) {
            console.log('⚠️ 部分Agent精力值偏低');
        } else {
            console.log('✅ 精力值在合理范围内');
        }
        console.log('');
        
        // 6. 总结修复效果
        console.log('6. 修复效果总结...');
        const fixes = [];
        const remaining = [];
        
        // 检查各项修复
        if (agents.length >= 2) {
            const avgDiff = agents.reduce((sum, agent, index) => {
                if (index === 0) return 0;
                const prevAgent = agents[index - 1];
                return sum + Math.abs(agent.focus - prevAgent.focus) + Math.abs(agent.energy - prevAgent.energy);
            }, 0) / (agents.length - 1) / 2;
            
            if (avgDiff > 5) {
                fixes.push('Agent差异性问题已修复');
            } else {
                remaining.push('Agent差异性仍需改进');
            }
        }
        
        if (avgEnergy > 20) {
            fixes.push('精力值异常低的问题已修复');
        } else {
            remaining.push('精力值仍然偏低');
        }
        
        if (maxFocus < 99 || minFocus < 90) {
            fixes.push('专注度固定100%的问题已修复');
        } else {
            remaining.push('专注度仍然偏高');
        }
        
        console.log('✅ 已修复的问题:');
        fixes.forEach((fix, index) => {
            console.log(`  ${index + 1}. ${fix}`);
        });
        
        if (remaining.length > 0) {
            console.log('\n⚠️ 仍需改进的问题:');
            remaining.forEach((issue, index) => {
                console.log(`  ${index + 1}. ${issue}`);
            });
        } else {
            console.log('\n🎉 所有关键问题都已修复！');
        }
        
        console.log('\n🔧 下一步建议:');
        console.log('1. 重启服务器以确保所有修复生效');
        console.log('2. 在管理界面观察长期变化趋势');
        console.log('3. 测试对话触发的心理状态更新');
        console.log('4. 检查内心独白的质量和连续性');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        if (error.code === 'ECONNREFUSED') {
            console.log('请确保服务器在 http://localhost:7700 运行');
        }
    }
}

// 运行测试
if (require.main === module) {
    testCriticalFixes().then(() => {
        console.log('\n🎯 测试完成！');
    }).catch(error => {
        console.error('测试失败:', error);
    });
}

module.exports = { testCriticalFixes };
