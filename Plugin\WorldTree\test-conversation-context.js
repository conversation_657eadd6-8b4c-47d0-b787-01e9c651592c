/**
 * 测试对话上下文功能
 * 验证：主程序触发时添加最近对话记录、配置项生效、上下文格式
 */

const axios = require('axios');

async function testConversationContext() {
    console.log('💬 测试对话上下文功能...\n');
    
    try {
        // 1. 检查配置项
        console.log('1. 检查配置项...');
        try {
            const statusResponse = await axios.get('http://localhost:7700/admin_api/worldtree/status');
            if (statusResponse.data.success) {
                const config = statusResponse.data.data.config;
                const contextCount = config.contextMessagesCount;
                console.log(`✅ 对话上下文记录数量配置: ${contextCount} 条`);
                
                if (contextCount === 6) {
                    console.log('✅ 默认配置正确');
                } else {
                    console.log(`⚠️ 配置值异常: ${contextCount}，预期: 6`);
                }
            } else {
                console.log('❌ 无法获取世界树配置');
            }
        } catch (error) {
            console.log(`⚠️ 配置检查失败: ${error.message}`);
        }
        console.log('');
        
        // 2. 发送多轮对话建立上下文
        console.log('2. 建立对话上下文...');
        const testUserId = 'test_conversation_context';
        const testAgent = '雨安安';
        
        const conversations = [
            '你好，今天天气怎么样？',
            '我正在学习机器学习，有什么建议吗？',
            '深度学习和传统机器学习有什么区别？',
            '你觉得AI的未来发展方向是什么？',
            '我想了解一下自然语言处理的应用场景'
        ];
        
        console.log(`发送 ${conversations.length} 轮对话...`);
        
        for (let i = 0; i < conversations.length; i++) {
            try {
                console.log(`  ${i + 1}. 发送: "${conversations[i].substring(0, 30)}..."`);
                
                const response = await axios.post('http://localhost:7700/v1/chat/completions', {
                    model: 'gpt-4o-mini',
                    messages: [
                        { role: 'user', content: conversations[i] }
                    ],
                    assistantName: testAgent,
                    userId: testUserId,
                    stream: false
                });
                
                if (response.status === 200) {
                    const aiResponse = response.data.choices[0].message.content;
                    console.log(`     回复: "${aiResponse.substring(0, 40)}..."`);
                } else {
                    console.log(`     ❌ 对话 ${i + 1} 失败`);
                }
                
                // 等待一下，让记忆系统处理
                await new Promise(resolve => setTimeout(resolve, 1000));
                
            } catch (error) {
                console.log(`     ⚠️ 对话 ${i + 1} 异常: ${error.message}`);
            }
        }
        console.log('');
        
        // 3. 等待心理状态更新
        console.log('3. 等待心理状态更新...');
        console.log('等待10秒让异步心理状态更新完成...');
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        // 4. 检查最新的心理活动记录
        console.log('4. 检查心理活动记录...');
        try {
            const logsResponse = await axios.get('http://localhost:7700/admin_api/worldtree/psychology/activities', {
                params: { limit: 3 }
            });
            
            if (logsResponse.data.success) {
                const logs = logsResponse.data.data;
                const recentLogs = logs.filter(log => log.agentName === testAgent);
                
                console.log(`找到 ${recentLogs.length} 条 ${testAgent} 的心理活动记录:`);
                
                recentLogs.forEach((log, index) => {
                    console.log(`\n${index + 1}. [${formatTime(log.created_time)}] ${log.generation_method}`);
                    console.log(`   内容: "${log.content?.substring(0, 100)}..."`);
                    
                    // 检查是否包含对话相关内容
                    const content = log.content || '';
                    const hasConversationRef = content.includes('对话') || content.includes('交流') || 
                                             content.includes('用户') || content.includes('问题') ||
                                             content.includes('机器学习') || content.includes('深度学习');
                    
                    if (hasConversationRef) {
                        console.log(`   ✅ 独白体现了对话内容`);
                    } else {
                        console.log(`   ⚠️ 独白未明显体现对话内容`);
                    }
                });
            } else {
                console.log('❌ 无法获取心理活动记录');
            }
        } catch (error) {
            console.log(`⚠️ 心理活动记录检查失败: ${error.message}`);
        }
        console.log('');
        
        // 5. 模拟检查User消息结构
        console.log('5. 预期的User消息结构...');
        console.log('当主程序触发心理状态更新时，User消息应该包含:');
        console.log('');
        console.log('=== 当前状态 ===');
        console.log('时间: 2024-07-22 15:30:00 (下午时段)');
        console.log('专注度: 45.2%');
        console.log('精力: 42.1%');
        console.log('...');
        console.log('');
        console.log('身心状态分析:');
        console.log('• 精力状态: 一般');
        console.log('• 疲劳感: 轻微');
        console.log('...');
        console.log('');
        console.log('=== 当前情境 ===');
        console.log('环境: AI实验室工作环境');
        console.log('日程: 代码实现和测试 (14:00-17:00)');
        console.log('');
        console.log('=== 最近对话记录 ===');
        console.log('1. [07/22 15:25]');
        console.log('   用户: "我想了解一下自然语言处理的应用场景"');
        console.log('   雨安安: "自然语言处理有很多实际应用场景，比如..."');
        console.log('');
        console.log('2. [07/22 15:23]');
        console.log('   用户: "你觉得AI的未来发展方向是什么？"');
        console.log('   雨安安: "AI的未来发展我认为会朝着几个方向..."');
        console.log('');
        console.log('...(最多6条记录)');
        console.log('注意: 基于最近的对话内容，生成符合当前情境的内心独白。');
        console.log('');
        console.log('=== 上一次独白 ===');
        console.log('"刚才与用户讨论机器学习的话题让我很兴奋..."');
        console.log('注意: 体现思维连续性，但不要重复。');
        console.log('');
        console.log('=== 请求 ===');
        console.log('请生成一段120-200字的内心独白，体现当前状态和真实感受。结合最近的对话内容，体现思维连续性，避免重复。不使用emoji。');
        console.log('');
        
        // 6. 检查功能区分
        console.log('6. 功能区分验证...');
        console.log('✅ 自动更新 (30分钟): 不包含对话上下文，只有状态和独白参考');
        console.log('✅ 对话触发 (立即): 包含完整对话上下文，基于最近交流生成独白');
        console.log('✅ 配置灵活: 可通过 WORLDTREE_CONTEXT_MESSAGES_COUNT 调整记录数量');
        console.log('✅ 性能优化: 异步执行，不阻塞对话响应');
        console.log('');
        
        // 7. 验证要点总结
        console.log('7. 验证要点总结...');
        
        const verificationPoints = [
            '配置项 WORLDTREE_CONTEXT_MESSAGES_COUNT 生效',
            '主程序触发时获取最近对话记录',
            '对话记录格式化为可读的上下文',
            '独白内容体现最近的对话主题',
            '自动更新和对话触发有明确区分',
            '异步执行不影响对话响应速度'
        ];
        
        console.log('🎯 需要验证的功能点:');
        verificationPoints.forEach((point, index) => {
            console.log(`  ${index + 1}. ${point}`);
        });
        
        console.log('\n📋 测试方法:');
        console.log('1. 与Agent进行多轮对话');
        console.log('2. 观察服务器日志的详细输出');
        console.log('3. 检查心理活动记录是否体现对话内容');
        console.log('4. 对比自动更新和对话触发的独白差异');
        console.log('5. 验证配置项修改是否生效');
        
        console.log('\n🎊 预期效果:');
        console.log('- 对话触发的独白会提到最近的交流内容');
        console.log('- 独白体现对对话主题的思考和感受');
        console.log('- 不同对话内容会产生不同风格的独白');
        console.log('- 独白保持Agent的个性特征和专业背景');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        if (error.code === 'ECONNREFUSED') {
            console.log('请确保服务器在 http://localhost:7700 运行');
        }
    }
}

// 辅助函数
function formatTime(timestamp) {
    if (!timestamp) return '--';
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 运行测试
if (require.main === module) {
    testConversationContext().then(() => {
        console.log('\n🎯 对话上下文功能测试完成！');
        console.log('\n💡 下一步：');
        console.log('1. 重启服务器应用更新');
        console.log('2. 与Agent进行多轮对话');
        console.log('3. 观察心理活动记录的变化');
        console.log('4. 验证独白是否体现对话内容');
    }).catch(error => {
        console.error('测试失败:', error);
    });
}

module.exports = { testConversationContext };
