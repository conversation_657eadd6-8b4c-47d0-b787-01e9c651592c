[{"timestamp": "2025-07-21T19:16:04.660776", "time_str": "07-21 19:16:04", "sender": "静", "type": "text", "content_data": "静说: @雨安 你好", "file_path": null, "content": "[群聊:测试][07-21 19:16:04] 静说: @雨安 你好"}, {"timestamp": "2025-07-21T19:18:08.316185", "time_str": "07-21 19:18:08", "sender": "静", "type": "text", "content_data": "静说: @雨安 你好", "file_path": null, "content": "[群聊:测试][07-21 19:18:08] 静说: @雨安 你好"}, {"timestamp": "2025-07-21T23:20:40.798973", "time_str": "07-21 23:20:40", "sender": "静", "type": "text", "content_data": "静说: @雨安 你好", "file_path": null, "content": "[群聊:测试][07-21 23:20:40] 静说: @雨安 你好"}, {"timestamp": "2025-07-21T23:20:53.093520", "time_str": "07-21 23:20:53", "sender": "静", "type": "quote", "content_data": "静引用了群聊中的图片![wxauto_image_20250721232052925141.jpg](D:\\Qbot\\vcpbot\\VCPToolBox\\WeChat\\wxautox文件下载\\wxauto_image_20250721232052925141.jpg)，然后说「@雨安 这是什么东西」", "file_path": null, "content": "[群聊:测试][07-21 23:20:53] 静引用了群聊中的图片![wxauto_image_20250721232052925141.jpg](D:\\Qbot\\vcpbot\\VCPToolBox\\WeChat\\wxautox文件下载\\wxauto_image_20250721232052925141.jpg)，然后说「@雨安 这是什么东西」"}]