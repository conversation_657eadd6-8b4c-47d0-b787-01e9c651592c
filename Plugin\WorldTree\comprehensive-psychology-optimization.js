/**
 * 全面优化心理状态算法
 * 解决不同Agent数值变化不一致的问题
 */

const path = require('path');
const fs = require('fs').promises;

async function comprehensivePsychologyOptimization() {
    console.log('🔧 全面优化心理状态算法...\n');
    
    let worldTreeVCP = null;
    
    try {
        // 1. 初始化插件
        console.log('1. 初始化世界树VCP插件...');
        const WorldTreeVCP = require('./WorldTreeVCP.js');
        worldTreeVCP = new WorldTreeVCP();
        
        const mockLogger = {
            info: (tag, ...args) => console.log(`[INFO] ${tag}:`, ...args),
            warning: (tag, ...args) => console.log(`[WARN] ${tag}:`, ...args),
            error: (tag, ...args) => console.log(`[ERROR] ${tag}:`, ...args),
            debug: (tag, ...args) => console.log(`[DEBUG] ${tag}:`, ...args)
        };
        
        const initResult = await worldTreeVCP.initialize(mockLogger);
        if (!initResult) {
            throw new Error('插件初始化失败');
        }
        console.log('✅ 插件初始化成功\n');
        
        // 2. 分析当前算法问题
        console.log('2. 分析当前算法问题...');
        const agents = await worldTreeVCP.dbAll(`SELECT agent_name FROM world_tree_configs`);
        
        if (agents.length < 2) {
            console.log('❌ 需要至少2个Agent进行分析');
            return;
        }
        
        console.log(`发现 ${agents.length} 个Agent，开始分析算法问题...`);
        
        // 测试多次计算的一致性
        const testResults = {};
        for (const agent of agents) {
            console.log(`\n📊 分析Agent: ${agent.agent_name}`);
            testResults[agent.agent_name] = [];
            
            // 连续计算5次，观察变化模式
            for (let i = 0; i < 5; i++) {
                const result = await worldTreeVCP.calculatePsychologyState('test_user', agent.agent_name, {
                    monitoringMode: true,
                    realTimeUpdate: true,
                    hasRecentConversation: false,
                    conversationLength: 0
                });
                
                testResults[agent.agent_name].push({
                    iteration: i + 1,
                    focus: result.focus || 0,
                    energy: result.energy || 0,
                    fatigue: result.fatigue || 0,
                    alertness: result.alertness || 0,
                    timestamp: Date.now()
                });
                
                // 等待100ms再计算下一次
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }
        
        // 分析变化模式
        console.log('\n📈 变化模式分析:');
        Object.entries(testResults).forEach(([agentName, results]) => {
            const focusVariation = Math.max(...results.map(r => r.focus)) - Math.min(...results.map(r => r.focus));
            const energyVariation = Math.max(...results.map(r => r.energy)) - Math.min(...results.map(r => r.energy));
            
            console.log(`  ${agentName}:`);
            console.log(`    专注度变化范围: ${focusVariation.toFixed(2)}`);
            console.log(`    精力变化范围: ${energyVariation.toFixed(2)}`);
            console.log(`    平均专注度: ${(results.reduce((sum, r) => sum + r.focus, 0) / results.length).toFixed(1)}`);
            console.log(`    平均精力: ${(results.reduce((sum, r) => sum + r.energy, 0) / results.length).toFixed(1)}`);
            
            if (focusVariation < 1 && energyVariation < 1) {
                console.log(`    ❌ 数值几乎静态，缺乏动态性`);
            } else if (focusVariation > 20 || energyVariation > 20) {
                console.log(`    ⚠️ 数值变化过大，可能不稳定`);
            } else {
                console.log(`    ✅ 数值变化适中`);
            }
        });
        
        // 3. 识别问题根源
        console.log('\n🔍 问题根源分析:');
        
        // 检查Agent特定种子的生成
        const agentSeeds = {};
        agents.forEach(agent => {
            const agentHash = agent.agent_name.split('').reduce((a, b) => {
                a = ((a << 5) - a) + b.charCodeAt(0);
                return a & a;
            }, 0);
            agentSeeds[agent.agent_name] = Math.abs(agentHash) / 1000000;
        });
        
        console.log('Agent种子值:');
        Object.entries(agentSeeds).forEach(([name, seed]) => {
            console.log(`  ${name}: ${seed.toFixed(6)}`);
        });
        
        // 检查种子值是否过于相似
        const seedValues = Object.values(agentSeeds);
        const maxSeedDiff = Math.max(...seedValues) - Math.min(...seedValues);
        if (maxSeedDiff < 0.1) {
            console.log('❌ Agent种子值过于相似，导致变化模式相同');
        } else {
            console.log('✅ Agent种子值有足够差异');
        }
        
        // 4. 检查时间因子影响
        console.log('\n⏰ 时间因子分析:');
        const currentTime = Date.now();
        const timeFactor = currentTime / 100000;
        
        agents.forEach(agent => {
            const agentSeed = agentSeeds[agent.agent_name];
            const timePhase = agentSeed * Math.PI * 2;
            const timeVariation = Math.sin(timeFactor + timePhase) * 15;
            
            console.log(`  ${agent.agent_name}:`);
            console.log(`    时间相位: ${timePhase.toFixed(3)}`);
            console.log(`    当前时间变化: ${timeVariation.toFixed(2)}`);
        });
        
        // 5. 检查算法配置
        console.log('\n⚙️ 算法配置检查:');
        console.log(`使用本地算法: ${worldTreeVCP.config.useLocalAlgorithm}`);
        console.log(`心理更新间隔: ${worldTreeVCP.config.psychologyUpdateInterval / 1000}秒`);
        console.log(`API配置: ${worldTreeVCP.config.apiKey ? '已配置' : '未配置'}`);
        
        // 6. 生成优化建议
        console.log('\n💡 优化建议:');
        
        const issues = [];
        const solutions = [];
        
        // 检查静态Agent
        const staticAgents = Object.entries(testResults).filter(([name, results]) => {
            const focusVar = Math.max(...results.map(r => r.focus)) - Math.min(...results.map(r => r.focus));
            return focusVar < 1;
        });
        
        if (staticAgents.length > 0) {
            issues.push(`${staticAgents.length}个Agent数值静态`);
            solutions.push('增强时间因子和随机因子的影响');
            solutions.push('优化Agent特定种子生成算法');
            solutions.push('添加更多动态变化源');
        }
        
        // 检查种子相似性
        if (maxSeedDiff < 0.1) {
            issues.push('Agent种子值过于相似');
            solutions.push('改进哈希算法，增加种子差异性');
            solutions.push('使用Agent名称长度和字符位置作为额外因子');
        }
        
        // 检查更新频率
        if (worldTreeVCP.config.psychologyUpdateInterval > 300000) {
            issues.push('心理状态更新间隔过长');
            solutions.push('缩短更新间隔到5分钟以内');
            solutions.push('增加实时变化的频率');
        }
        
        console.log('发现的问题:');
        issues.forEach((issue, index) => {
            console.log(`  ${index + 1}. ${issue}`);
        });
        
        console.log('\n建议的解决方案:');
        solutions.forEach((solution, index) => {
            console.log(`  ${index + 1}. ${solution}`);
        });
        
        // 7. 提供具体的代码修复建议
        console.log('\n🔧 具体修复建议:');
        console.log('');
        console.log('1. 优化Agent种子生成 (server.js):');
        console.log('   - 使用更复杂的哈希算法');
        console.log('   - 结合Agent名称长度和字符分布');
        console.log('   - 确保种子值在0.1-2.0范围内有足够分布');
        console.log('');
        console.log('2. 增强时间变化因子 (WorldTreeVCP.js):');
        console.log('   - 使用多个不同周期的正弦波叠加');
        console.log('   - 添加Agent特定的变化幅度');
        console.log('   - 引入更多随机性但保持可预测性');
        console.log('');
        console.log('3. 优化监控模式算法:');
        console.log('   - 确保每个Agent都有独特的变化模式');
        console.log('   - 平衡静态基础值和动态变化');
        console.log('   - 添加渐进式变化而非突变');
        console.log('');
        console.log('4. 改进实时更新机制:');
        console.log('   - 缩短更新间隔到2-5分钟');
        console.log('   - 添加微小的连续变化');
        console.log('   - 确保所有Agent同步更新');
        
        // 8. 测试优化后的效果
        console.log('\n🧪 测试优化效果建议:');
        console.log('1. 重启服务器应用修复');
        console.log('2. 观察每个Agent的数值变化');
        console.log('3. 确认刷新时所有Agent都有微小变化');
        console.log('4. 验证不同Agent的变化模式确实不同');
        console.log('5. 检查长期运行的稳定性');
        
    } catch (error) {
        console.error('❌ 优化分析过程中发生错误:', error.message);
        console.error('错误堆栈:', error.stack);
    } finally {
        // 清理资源
        if (worldTreeVCP && worldTreeVCP.psychologyTimer) {
            clearInterval(worldTreeVCP.psychologyTimer);
        }
        if (worldTreeVCP && worldTreeVCP.db) {
            worldTreeVCP.db.close();
        }
    }
}

// 运行优化分析
if (require.main === module) {
    comprehensivePsychologyOptimization().then(() => {
        console.log('\n🎯 优化分析完成！');
        process.exit(0);
    }).catch(error => {
        console.error('优化分析失败:', error);
        process.exit(1);
    });
}

module.exports = { comprehensivePsychologyOptimization };
