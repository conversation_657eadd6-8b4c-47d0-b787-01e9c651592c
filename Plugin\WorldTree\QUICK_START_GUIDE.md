# 世界树VCP插件快速启动指南

## 🚀 启动步骤

### 1. 启动服务器
在项目根目录下运行：
```bash
# 方法1: 使用npm
npm start

# 方法2: 直接运行
node server.js
```

### 2. 验证服务器启动
看到以下信息表示启动成功：
```
服务器启动在端口 7700
管理面板: http://localhost:7700/AdminPanel
```

### 3. 访问管理界面
打开浏览器访问：`http://localhost:7700/AdminPanel`

### 4. 检查世界树插件状态
1. 点击左侧菜单"世界树VCP"
2. 查看插件状态是否显示"运行中"
3. 如果显示"未初始化"，请检查插件配置

## 🧪 测试导出导入功能

### 测试导出功能
1. 确保至少有一个Agent已配置世界树
2. 在世界树VCP管理页面的"Agent配置"标签页
3. 点击某个Agent的"编辑"按钮（重要：必须先选择Agent）
4. 点击"导出配置"按钮
5. 应该自动下载该Agent的JSON配置文件

### 测试导入功能
1. 使用提供的示例文件：`Plugin/WorldTree/example-import-config.json`
2. 在世界树VCP管理页面的"Agent配置"标签页
3. 点击某个Agent的"编辑"按钮（选择目标Agent）
4. 点击"导入配置"按钮
5. 选择示例文件进行导入
6. 确认将示例配置应用到当前选择的Agent

## 🔍 故障排除

### 导出功能返回undefined
**浏览器调试步骤：**

1. **确保已选择Agent**
   - 必须先点击某个Agent的"编辑"按钮
   - 如果没有选择Agent，会提示"请先选择一个Agent"

2. **打开开发者工具**
   - 按F12或右键选择"检查"
   - 切换到Console标签页

3. **点击导出配置按钮**
   - 观察控制台输出
   - 应该看到以下信息：
     ```
     开始导出Agent配置: [Agent名称]
     获取配置API响应状态: 200
     获取配置API响应数据: {...}
     准备下载的数据: {...}
     配置导出完成
     ```

4. **检查Network标签页**
   - 切换到Network标签页
   - 点击导出按钮
   - 查找`/admin_api/worldtree/configs/[AgentName]`请求
   - 检查状态码和响应内容

5. **常见问题解决**
   - **未选择Agent**: 先点击Agent的"编辑"按钮
   - **Agent无配置**: 先为Agent创建世界树配置
   - **浏览器阻止下载**: 检查下载设置
   - **JavaScript错误**: 查看控制台错误信息

### 没有Agent配置数据
**创建测试配置：**

1. 在"Agent配置"标签页点击"新建配置"
2. 选择一个Agent（如Nova）
3. 填写基本配置：
   ```
   世界背景: 测试世界背景
   时间架构 - 早晨: 测试早晨设定
   角色日程 - 09:00-12:00: 测试工作时间
   叙事规则 - 测试规则: 测试描述
   ```
4. 点击"保存配置"

### 服务器启动失败
**检查端口占用：**
```bash
# Windows
netstat -ano | findstr :7700

# Linux/Mac
lsof -i :7700
```

**更换端口：**
如果7700端口被占用，可以修改`server.js`中的端口设置。

## 📋 完整测试流程

### 1. 环境准备
```bash
# 确保在项目根目录
cd /path/to/VCPToolBox

# 启动服务器
node server.js
```

### 2. 创建测试配置
1. 访问 http://localhost:7700/AdminPanel
2. 点击"世界树VCP" → "Agent配置"
3. 创建一个测试Agent配置

### 3. 测试导出
1. 切换到"Agent配置"标签页
2. 点击某个Agent的"编辑"按钮
3. 点击"导出配置"按钮
4. 验证下载的文件名格式：`worldtree-[AgentName]-[日期].json`

### 4. 测试导入
1. 保持在Agent编辑模式
2. 点击"导入配置"按钮
3. 选择`example-import-config.json`文件
4. 确认导入提示信息
5. 验证配置是否正确应用

### 5. 验证结果
1. 检查Agent列表是否包含导入的配置
2. 查看配置详情是否正确

## 🛠️ 调试工具

### 离线测试
```bash
# 检查代码修复
node Plugin/WorldTree/test-fixes-offline.js

# 调试导出问题（需要服务器运行）
node Plugin/WorldTree/debug-export-issue.js

# 测试单Agent导出导入功能（需要服务器运行）
node Plugin/WorldTree/test-single-agent-export-import.js

# 完整功能测试（需要服务器运行）
node Plugin/WorldTree/test-export-import.js
```

### 日志查看
- 服务器控制台会显示详细的操作日志
- 浏览器控制台会显示前端调试信息
- 检查这些日志可以快速定位问题

## ✅ 成功标志

当看到以下情况时，表示功能正常：

1. **插件状态**: 显示"运行中"而不是"检查中"
2. **导出功能**: 点击后自动下载JSON文件
3. **导入功能**: 显示导入成功统计信息
4. **配置持久化**: 配置不会突然变成默认值
5. **布尔配置**: WORLDTREE_SHOW_ENERGY等显示为true/false而不是文本

## 📞 获取帮助

如果遇到问题：
1. 查看服务器控制台日志
2. 查看浏览器开发者工具
3. 运行调试脚本获取详细信息
4. 检查配置文件格式是否正确

所有修复都已完成，按照此指南应该能够正常使用所有功能！
