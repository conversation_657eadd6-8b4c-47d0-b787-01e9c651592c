/**
 * 诊断关键问题：为什么只有一个Agent生效，专注度不变化，精力值异常
 */

const axios = require('axios');

async function diagnoseCriticalIssues() {
    console.log('🔍 诊断关键问题...\n');
    
    try {
        // 1. 检查API返回的原始数据
        console.log('1. 检查API返回的原始数据...');
        const response = await axios.get('http://localhost:7700/admin_api/worldtree/psychology/realtime');
        
        if (response.data.success) {
            const agents = response.data.data;
            console.log(`API返回 ${agents.length} 个Agent:`);
            
            agents.forEach((agent, index) => {
                console.log(`\n${index + 1}. Agent: ${agent.agentName}`);
                console.log(`   专注度: ${agent.focus}`);
                console.log(`   精力: ${agent.energy}`);
                console.log(`   疲劳: ${agent.fatigue}`);
                console.log(`   警觉: ${agent.alertness}`);
                console.log(`   饥饿: ${agent.hunger}`);
                console.log(`   时间段: ${agent.timePeriod}`);
                console.log(`   更新时间: ${agent.lastUpdate}`);
            });
            
            // 检查数值是否有差异
            if (agents.length >= 2) {
                const agent1 = agents[0];
                const agent2 = agents[1];
                
                console.log('\n📊 Agent差异分析:');
                console.log(`专注度差异: ${Math.abs(agent1.focus - agent2.focus).toFixed(2)}`);
                console.log(`精力差异: ${Math.abs(agent1.energy - agent2.energy).toFixed(2)}`);
                console.log(`疲劳差异: ${Math.abs(agent1.fatigue - agent2.fatigue).toFixed(2)}`);
                console.log(`警觉差异: ${Math.abs(agent1.alertness - agent2.alertness).toFixed(2)}`);
                
                if (Math.abs(agent1.focus - agent2.focus) < 0.1 && 
                    Math.abs(agent1.energy - agent2.energy) < 0.1) {
                    console.log('❌ 两个Agent的数值几乎完全相同！');
                } else {
                    console.log('✅ Agent数值有差异');
                }
            }
        } else {
            console.log('❌ API调用失败:', response.data.error);
            return;
        }
        console.log('');
        
        // 2. 连续监控数值变化
        console.log('2. 连续监控数值变化（30秒）...');
        const initialData = response.data.data;
        
        console.log('等待30秒观察变化...');
        await new Promise(resolve => setTimeout(resolve, 30000));
        
        const secondResponse = await axios.get('http://localhost:7700/admin_api/worldtree/psychology/realtime');
        if (secondResponse.data.success) {
            const updatedData = secondResponse.data.data;
            
            console.log('📈 30秒后的变化:');
            initialData.forEach((initialAgent, index) => {
                const updatedAgent = updatedData.find(a => a.agentName === initialAgent.agentName);
                if (updatedAgent) {
                    const focusChange = Math.abs(updatedAgent.focus - initialAgent.focus);
                    const energyChange = Math.abs(updatedAgent.energy - initialAgent.energy);
                    const fatigueChange = Math.abs(updatedAgent.fatigue - initialAgent.fatigue);
                    const alertnessChange = Math.abs(updatedAgent.alertness - initialAgent.alertness);
                    
                    console.log(`\n${initialAgent.agentName}:`);
                    console.log(`  专注度: ${initialAgent.focus.toFixed(2)} → ${updatedAgent.focus.toFixed(2)} (变化: ${focusChange.toFixed(2)})`);
                    console.log(`  精力: ${initialAgent.energy.toFixed(2)} → ${updatedAgent.energy.toFixed(2)} (变化: ${energyChange.toFixed(2)})`);
                    console.log(`  疲劳: ${initialAgent.fatigue.toFixed(2)} → ${updatedAgent.fatigue.toFixed(2)} (变化: ${fatigueChange.toFixed(2)})`);
                    console.log(`  警觉: ${initialAgent.alertness.toFixed(2)} → ${updatedAgent.alertness.toFixed(2)} (变化: ${alertnessChange.toFixed(2)})`);
                    
                    if (focusChange < 0.1 && energyChange < 0.1 && fatigueChange < 0.1 && alertnessChange < 0.1) {
                        console.log(`  ❌ ${initialAgent.agentName} 完全静态，没有任何变化！`);
                    } else {
                        console.log(`  ✅ ${initialAgent.agentName} 有数值变化`);
                    }
                }
            });
        }
        console.log('');
        
        // 3. 检查数据库配置
        console.log('3. 检查数据库配置...');
        try {
            const configResponse = await axios.get('http://localhost:7700/admin_api/worldtree/status');
            if (configResponse.data.success) {
                const status = configResponse.data.data;
                console.log('世界树VCP状态:');
                console.log(`  初始化状态: ${status.isInitialized}`);
                console.log(`  使用本地算法: ${status.config.useLocalAlgorithm}`);
                console.log(`  API配置: ${status.config.hasApiConfig}`);
                console.log(`  配置的Agent数量: ${status.statistics.configuredAgents}`);
                console.log(`  更新间隔: ${status.statistics.updateInterval}秒`);
            }
        } catch (error) {
            console.log('❌ 无法获取世界树状态:', error.message);
        }
        console.log('');
        
        // 4. 检查Agent配置列表
        console.log('4. 检查Agent配置列表...');
        try {
            const agentListResponse = await axios.get('http://localhost:7700/admin_api/worldtree/agents');
            if (agentListResponse.data.success) {
                const agentList = agentListResponse.data.data;
                console.log(`发现 ${agentList.length} 个Agent文件:`);
                
                agentList.forEach(agent => {
                    console.log(`  - ${agent.name}: 配置状态 ${agent.hasWorldTreeConfig ? '✅' : '❌'}`);
                });
                
                const configuredCount = agentList.filter(agent => agent.hasWorldTreeConfig).length;
                console.log(`\n配置的Agent数量: ${configuredCount}/${agentList.length}`);
                
                if (configuredCount < agentList.length) {
                    console.log('⚠️ 有Agent未配置世界树，这可能是问题原因');
                }
            }
        } catch (error) {
            console.log('❌ 无法获取Agent列表:', error.message);
        }
        console.log('');
        
        // 5. 检查心理活动日志
        console.log('5. 检查心理活动日志...');
        try {
            const logsResponse = await axios.get('http://localhost:7700/admin_api/worldtree/psychology/activities', {
                params: { limit: 10 }
            });
            
            if (logsResponse.data.success) {
                const logs = logsResponse.data.data;
                console.log(`获取到 ${logs.length} 条心理活动记录`);
                
                if (logs.length > 0) {
                    const agentCounts = {};
                    logs.forEach(log => {
                        agentCounts[log.agentName] = (agentCounts[log.agentName] || 0) + 1;
                    });
                    
                    console.log('各Agent的记录数量:');
                    Object.entries(agentCounts).forEach(([agentName, count]) => {
                        console.log(`  ${agentName}: ${count} 条记录`);
                    });
                    
                    // 检查最新记录
                    const latestLog = logs[0];
                    console.log(`\n最新记录 (${latestLog.agentName}):`);
                    console.log(`  时间: ${latestLog.created_time}`);
                    console.log(`  内容: "${latestLog.content?.substring(0, 100)}..."`);
                    console.log(`  生成方式: ${latestLog.generation_method}`);
                } else {
                    console.log('❌ 没有心理活动记录');
                }
            }
        } catch (error) {
            console.log('❌ 无法获取心理活动日志:', error.message);
        }
        console.log('');
        
        // 6. 问题总结和建议
        console.log('6. 问题总结和建议...');
        console.log('🔍 发现的问题:');
        
        const problems = [];
        const solutions = [];
        
        // 基于前面的检查结果给出具体建议
        if (initialData.length < 2) {
            problems.push('只有一个Agent在工作');
            solutions.push('检查Agent配置，确保所有Agent都有世界树配置');
        }
        
        if (initialData.some(agent => agent.energy < 1)) {
            problems.push('精力值异常（接近0）');
            solutions.push('修复精力计算算法，确保合理范围');
        }
        
        if (initialData.some(agent => agent.focus === 100)) {
            problems.push('专注度固定在100%');
            solutions.push('修复专注度算法，添加动态变化');
        }
        
        problems.forEach((problem, index) => {
            console.log(`  ${index + 1}. ${problem}`);
        });
        
        console.log('\n💡 建议的解决方案:');
        solutions.forEach((solution, index) => {
            console.log(`  ${index + 1}. ${solution}`);
        });
        
        console.log('\n🔧 立即修复步骤:');
        console.log('1. 检查所有Agent是否都有世界树配置');
        console.log('2. 修复精力计算算法的数值范围问题');
        console.log('3. 修复专注度和警觉度的动态变化算法');
        console.log('4. 确保每个Agent有独特的变化模式');
        console.log('5. 重启服务器应用修复');
        
    } catch (error) {
        console.error('❌ 诊断过程中发生错误:', error.message);
    }
}

// 运行诊断
if (require.main === module) {
    diagnoseCriticalIssues().then(() => {
        console.log('\n🎯 诊断完成！');
    }).catch(error => {
        console.error('诊断失败:', error);
    });
}

module.exports = { diagnoseCriticalIssues };
