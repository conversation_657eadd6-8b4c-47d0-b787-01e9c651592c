# 世界树VCP插件使用指南

## 🎯 优化后的功能

### 1. 插件状态监控
- **实时状态显示**：插件状态不再一直显示"检查中"
- **详细状态信息**：显示初始化状态、配置Agent数量、数据库状态等
- **自动状态更新**：页面加载时自动检查插件状态

### 2. 配置导出功能
- **一键导出**：点击"导出配置"按钮即可下载JSON文件
- **完整数据**：包含所有Agent的世界树配置
- **导出信息**：显示配置数量、导出时间、版本信息

### 3. 配置导入功能 🆕
- **文件导入**：支持JSON格式的配置文件导入
- **覆盖模式**：可选择覆盖已存在的配置或跳过
- **批量处理**：一次导入多个Agent配置
- **结果统计**：显示导入成功和失败的数量

### 4. 配置类型优化
- **布尔值配置**：WORLDTREE_SHOW_ENERGY等配置项现在是正确的布尔类型
- **类型安全**：配置解析逻辑确保类型正确性
- **默认值**：所有新配置项都有合理的默认值

### 5. 配置持久化增强
- **深拷贝机制**：避免配置引用问题导致的数据丢失
- **缓存管理**：智能缓存过期和清理机制
- **错误恢复**：自动检测和清理损坏的缓存

## 🚀 使用方法

### 启动服务器
```bash
# 在项目根目录下
npm start
# 或
node server.js
```

### 访问管理界面
1. 打开浏览器访问：`http://localhost:7700/AdminPanel`
2. 点击左侧菜单"世界树VCP"
3. 查看插件状态和管理配置

### 配置导出
1. 在世界树VCP管理页面的"Agent配置"标签页
2. 点击某个Agent的"编辑"按钮进入编辑模式
3. 点击右上角"导出配置"按钮
4. 自动下载当前Agent的JSON格式配置文件
5. **注意**：必须先选择Agent才能导出，否则会提示选择Agent

### 配置导入
1. 在世界树VCP管理页面的"Agent配置"标签页
2. 点击某个Agent的"编辑"按钮进入编辑模式
3. 点击右上角"导入配置"按钮
4. 选择JSON格式的单Agent配置文件
5. 确认是否将导入的配置应用到当前选择的Agent
6. **注意**：导入会覆盖当前Agent的配置

### 缓存管理
1. 切换到"系统状态"标签页
2. 点击"清理缓存"按钮
3. 确认清理所有配置缓存

## 📋 新增配置项

### 显示配置
```env
# 是否显示精力水平
WORLDTREE_SHOW_ENERGY=true

# 是否显示心理状态详情
WORLDTREE_SHOW_PSYCHOLOGY_DETAILS=true

# 是否显示时间架构信息
WORLDTREE_SHOW_TIME_ARCHITECTURE=true

# 是否显示角色日程表
WORLDTREE_SHOW_CHARACTER_SCHEDULES=true
```

### 配置说明
- 所有显示配置项都是布尔类型
- 默认值都为`true`
- 可以通过配置文件或环境变量设置

## 🔧 故障排除

### 插件状态显示异常
1. 检查服务器是否正常启动
2. 查看控制台是否有错误信息
3. 尝试刷新页面或清理浏览器缓存

### 配置导出返回undefined
**常见原因和解决方法：**

1. **检查浏览器控制台**
   - 按F12打开开发者工具
   - 查看Console标签页是否有错误信息
   - 查看Network标签页检查API请求状态

2. **检查服务器状态**
   ```bash
   # 运行调试脚本
   node Plugin/WorldTree/debug-export-issue.js
   ```

3. **检查Agent配置**
   - 确保至少有一个Agent已配置
   - 在"Agent配置"标签页创建测试配置

4. **浏览器下载设置**
   - 检查浏览器是否阻止了文件下载
   - 确认下载文件夹权限正常

### 配置导入失败
1. **文件格式检查**
   - 确认是JSON格式文件
   - 使用提供的示例文件测试
   - 检查JSON语法是否正确

2. **文件内容验证**
   ```json
   {
     "success": true,
     "configs": [
       {
         "agentName": "Agent名称",
         "config": { ... }
       }
     ]
   }
   ```

3. **权限问题**
   - 确认服务器有写入权限
   - 检查数据库连接状态

### 配置突然变成默认值
- 这个问题已经修复！
- 新的深拷贝机制和缓存管理确保配置持久化
- 如果仍然遇到问题，可以尝试清理缓存

## 📊 API接口

### 状态查询
```
GET /admin_api/worldtree/status
```

### 配置导出
```
GET /admin_api/worldtree/configs/export
```

### 配置导入
```
POST /admin_api/worldtree/configs/import
Content-Type: application/json

{
  "configs": [...],
  "overwrite": true
}
```

### 缓存清理
```
POST /admin_api/worldtree/cache/clear
Content-Type: application/json

{
  "agentName": "可选，指定Agent名称"
}
```

## 🧪 测试验证

### 离线测试
```bash
node Plugin/WorldTree/test-fixes-offline.js
```

### 在线测试（需要服务器运行）
```bash
node Plugin/WorldTree/test-optimization-fixes.js
```

## 📝 配置文件格式详解

### 单Agent配置文件结构
导出的配置文件是标准JSON格式，包含单个Agent的完整配置：

```json
{
  "agentName": "Agent名称",    // 原始Agent名称
  "config": {...},           // 完整配置数据
  "exportTime": "时间戳",     // 导出时间
  "version": "版本号"         // 配置版本
}
```

### 完整配置文件示例
```json
{
  "agentName": "Nova",
  "config": {
    "config": {
      "showEnergy": true,
      "showPsychologyDetails": true,
      "showTimeArchitecture": true,
      "showCharacterSchedules": true
    },
    "timeArchitecture": {
      "morning": "清晨时分，思维最为清晰，适合进行复杂的分析工作",
      "afternoon": "下午时光，精力充沛，适合处理日常事务和交流",
      "evening": "傍晚时刻，开始放松，适合创意思考和总结",
      "night": "夜深人静，适合深度思考和内省"
    },
    "characterSchedules": {
      "06:00-08:00": "晨间冥想和计划制定",
      "08:00-12:00": "核心工作时间，处理重要任务",
      "12:00-13:00": "午餐休息",
      "13:00-17:00": "下午工作时间，处理日常事务",
      "17:00-19:00": "傍晚放松时间",
      "19:00-21:00": "晚餐和社交时间",
      "21:00-23:00": "个人时间，阅读和思考",
      "23:00-06:00": "休息睡眠"
    },
    "worldBackground": "Nova是一个高度智能的AI助手，具有深度学习能力和情感理解能力。她生活在一个数字化的世界中，能够感知用户的情绪变化，并根据不同的时间段调整自己的行为模式。Nova注重效率和人性化的交互体验。",
    "narrativeRules": {
      "情感表达": "根据用户的情绪状态调整回应的语调和内容",
      "时间感知": "根据当前时间段调整行为模式和建议",
      "个性化": "记住用户的偏好和习惯，提供个性化的服务",
      "专业性": "在专业领域保持准确性和权威性",
      "人性化": "在交流中体现温暖和理解"
    },
    "createdTime": "2024-01-15T10:30:00.000Z",
    "updatedTime": "2024-01-15T10:30:00.000Z"
  },
  "exportTime": "2024-01-15T10:30:00.000Z",
  "version": "1.0.0"
}
```

### 字段说明

#### 显示配置 (config.config)
- `showEnergy`: 是否显示精力水平 (布尔值)
- `showPsychologyDetails`: 是否显示心理状态详情 (布尔值)
- `showTimeArchitecture`: 是否显示时间架构信息 (布尔值)
- `showCharacterSchedules`: 是否显示角色日程表 (布尔值)

#### 时间架构 (timeArchitecture)
定义不同时间段的特征和氛围：
- `morning`: 早晨时段描述
- `afternoon`: 下午时段描述
- `evening`: 傍晚时段描述
- `night`: 夜晚时段描述

#### 角色日程 (characterSchedules)
定义具体的时间安排，格式为 "开始时间-结束时间": "活动描述"

#### 世界背景 (worldBackground)
Agent的背景设定和世界观描述

#### 叙事规则 (narrativeRules)
定义Agent的行为准则和交互规则

### 导入注意事项
1. **文件编码**: 必须是UTF-8编码
2. **JSON格式**: 必须是有效的JSON语法
3. **必需字段**: `agentName`和`config`字段是必需的
4. **Agent选择**: 导入前必须先选择目标Agent
5. **配置覆盖**: 导入会完全覆盖目标Agent的现有配置
6. **布尔值**: 显示配置项必须是布尔类型，不能是字符串

### 导入流程
1. **选择目标Agent**: 在Agent配置页面点击"编辑"按钮
2. **选择配置文件**: 点击"导入配置"选择JSON文件
3. **确认导入**: 系统会显示源Agent名称和目标Agent名称
4. **应用配置**: 确认后配置会应用到当前选择的Agent

### 示例文件
项目中提供了示例文件：
- `Plugin/WorldTree/example-import-config.json` - 单Agent配置导入示例

## ✅ 验证清单

使用前请确认：
- [x] 服务器正常启动
- [x] 世界树VCP插件已初始化
- [x] 管理界面可以正常访问
- [x] 插件状态显示正常
- [x] 配置导出功能正常
- [x] 配置导入功能正常
- [x] 缓存清理功能正常

## 🎉 享受优化后的体验！

所有问题都已修复，现在可以稳定使用世界树VCP插件的完整功能了！
