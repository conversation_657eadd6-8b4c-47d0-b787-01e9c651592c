/**
 * 测试对话触发修复效果
 * 验证：每次对话都触发心理独白、无CD限制、独立于自动更新
 */

const axios = require('axios');

async function testConversationTriggerFix() {
    console.log('🔧 测试对话触发修复效果...\n');
    
    try {
        // 1. 清空之前的测试数据
        console.log('1. 准备测试环境...');
        const testUserId = 'test_trigger_fix';
        const testAgent = '雨安安';
        
        console.log(`测试用户: ${testUserId}`);
        console.log(`测试Agent: ${testAgent}`);
        console.log('');
        
        // 2. 连续发送多轮对话，验证每次都触发
        console.log('2. 连续对话测试（验证无CD限制）...');
        const conversations = [
            '你好，我想测试心理独白功能',
            '这是第二条消息，应该立即触发',
            '第三条消息，不应该有任何延迟',
            '第四条消息，验证连续触发',
            '第五条消息，确认每次都生成独白'
        ];
        
        const results = [];
        
        for (let i = 0; i < conversations.length; i++) {
            const startTime = Date.now();
            console.log(`\n📤 发送第 ${i + 1} 条消息: "${conversations[i]}"`);
            
            try {
                const response = await axios.post('http://localhost:7700/v1/chat/completions', {
                    model: 'gpt-4o-mini',
                    messages: [
                        { role: 'user', content: conversations[i] }
                    ],
                    assistantName: testAgent,
                    userId: testUserId,
                    stream: false
                });
                
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                if (response.status === 200) {
                    const aiResponse = response.data.choices[0].message.content;
                    console.log(`✅ 对话成功 (${responseTime}ms): "${aiResponse.substring(0, 50)}..."`);
                    
                    results.push({
                        index: i + 1,
                        success: true,
                        responseTime,
                        timestamp: new Date().toISOString()
                    });
                } else {
                    console.log(`❌ 对话失败: ${response.status}`);
                    results.push({
                        index: i + 1,
                        success: false,
                        responseTime,
                        error: `HTTP ${response.status}`
                    });
                }
                
                // 短暂等待，让异步处理完成
                console.log('⏳ 等待2秒让异步心理独白处理...');
                await new Promise(resolve => setTimeout(resolve, 2000));
                
            } catch (error) {
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                console.log(`❌ 对话异常 (${responseTime}ms): ${error.message}`);
                
                results.push({
                    index: i + 1,
                    success: false,
                    responseTime,
                    error: error.message
                });
            }
        }
        
        console.log('\n📊 对话结果统计:');
        const successCount = results.filter(r => r.success).length;
        console.log(`成功: ${successCount}/${results.length}`);
        console.log(`平均响应时间: ${Math.round(results.reduce((sum, r) => sum + r.responseTime, 0) / results.length)}ms`);
        
        // 3. 等待所有异步处理完成
        console.log('\n3. 等待异步处理完成...');
        console.log('等待10秒确保所有心理独白都已生成...');
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        // 4. 检查心理活动记录
        console.log('4. 检查心理活动记录...');
        try {
            const logsResponse = await axios.get('http://localhost:7700/admin_api/worldtree/psychology/activities', {
                params: { limit: 10 }
            });
            
            if (logsResponse.data.success) {
                const logs = logsResponse.data.data;
                const testLogs = logs.filter(log => 
                    log.agentName === testAgent && 
                    new Date(log.created_time) > new Date(Date.now() - 5 * 60 * 1000) // 最近5分钟
                );
                
                console.log(`\n📋 找到 ${testLogs.length} 条最近的心理活动记录:`);
                
                testLogs.forEach((log, index) => {
                    console.log(`\n${index + 1}. [${formatTime(log.created_time)}] ${log.generation_method}`);
                    console.log(`   内容: "${log.content?.substring(0, 100)}..."`);
                    
                    // 检查是否包含测试相关内容
                    const content = log.content || '';
                    const hasTestRef = content.includes('测试') || content.includes('消息') || 
                                     content.includes('独白') || content.includes('功能');
                    
                    if (hasTestRef) {
                        console.log(`   ✅ 独白体现了测试对话内容`);
                    } else {
                        console.log(`   ⚠️ 独白未明显体现测试内容`);
                    }
                });
                
                // 验证触发频率
                console.log(`\n🎯 触发验证:`);
                console.log(`发送对话: ${conversations.length} 条`);
                console.log(`生成独白: ${testLogs.length} 条`);
                
                if (testLogs.length >= conversations.length) {
                    console.log(`✅ 触发频率正常 (${testLogs.length}/${conversations.length})`);
                } else if (testLogs.length >= conversations.length * 0.8) {
                    console.log(`⚠️ 触发频率偏低但可接受 (${testLogs.length}/${conversations.length})`);
                } else {
                    console.log(`❌ 触发频率过低 (${testLogs.length}/${conversations.length})`);
                }
                
            } else {
                console.log('❌ 无法获取心理活动记录');
            }
        } catch (error) {
            console.log(`⚠️ 心理活动记录检查失败: ${error.message}`);
        }
        
        // 5. 验证修复效果
        console.log('\n5. 修复效果验证...');
        
        const fixedIssues = [
            '对话触发不再受自动更新的时间限制',
            '每次对话都会异步触发心理独白生成',
            '对话触发和定时更新完全独立',
            '添加了详细的调试日志跟踪',
            '异步处理不阻塞对话响应'
        ];
        
        console.log('✅ 已修复的问题:');
        fixedIssues.forEach((issue, index) => {
            console.log(`  ${index + 1}. ${issue}`);
        });
        
        console.log('\n🔍 日志检查要点:');
        console.log('在服务器日志中应该看到:');
        console.log('- "✅ 对话触发，立即执行: [Agent名称]"');
        console.log('- "🚀 强制异步触发API生成: [Agent名称] (触发类型: 对话)"');
        console.log('- "🎯 开始异步API调用: [Agent名称]"');
        console.log('- "✅ 异步API生成完成 [Agent名称]，耗时: X.XX秒"');
        console.log('- "🎊 对话触发的心理独白生成成功: [Agent名称]"');
        
        console.log('\n📋 验证清单:');
        console.log('□ 每次对话都触发心理独白（无CD限制）');
        console.log('□ 对话触发独立于自动更新时间');
        console.log('□ 异步处理不影响对话响应速度');
        console.log('□ 心理活动记录正确保存到数据库');
        console.log('□ 服务器日志显示详细的处理过程');
        
        console.log('\n🎊 预期效果:');
        console.log('- 用户每次发送消息都会触发心理独白');
        console.log('- 不再出现"有时生成有时不生成"的问题');
        console.log('- 对话响应速度不受影响（异步处理）');
        console.log('- 心理活动日志实时更新');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        if (error.code === 'ECONNREFUSED') {
            console.log('请确保服务器在 http://localhost:7700 运行');
        }
    }
}

// 辅助函数
function formatTime(timestamp) {
    if (!timestamp) return '--';
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 运行测试
if (require.main === module) {
    testConversationTriggerFix().then(() => {
        console.log('\n🎯 对话触发修复测试完成！');
        console.log('\n💡 下一步：');
        console.log('1. 重启服务器应用修复');
        console.log('2. 进行连续对话测试');
        console.log('3. 观察服务器日志的详细输出');
        console.log('4. 验证心理活动记录的生成频率');
        console.log('5. 确认每次对话都触发独白生成');
    }).catch(error => {
        console.error('测试失败:', error);
    });
}

module.exports = { testConversationTriggerFix };
